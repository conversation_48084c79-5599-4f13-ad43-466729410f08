{"timestamp": "2025-06-01T10:42:43.869724", "checks": {"docker_containers": {"hvac-weaviate": {"status": "running", "health": "unknown", "created": "2025-05-31T21:53:10.421370998Z", "image": "semitechnologies/weaviate:1.25.0", "ports": {"50051/tcp": [{"HostIp": "0.0.0.0", "HostPort": "50051"}, {"HostIp": "::", "HostPort": "50051"}], "8080/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8082"}, {"HostIp": "::", "HostPort": "8082"}]}, "restart_count": 0}, "hvac-embeddings": {"status": "running", "health": "unknown", "created": "2025-05-31T21:53:10.113423257Z", "image": "cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2", "ports": {}, "restart_count": 0}, "hvac-redis": {"status": "running", "health": "unknown", "created": "2025-06-01T06:47:06.29115899Z", "image": "redis:7-alpine", "ports": {"6379/tcp": [{"HostIp": "0.0.0.0", "HostPort": "6379"}, {"HostIp": "::", "HostPort": "6379"}]}, "restart_count": 0}, "hvac-mongodb-local": {"status": "running", "health": "unknown", "created": "2025-05-30T12:24:23.707795212Z", "image": "mongo:7", "ports": {"27017/tcp": [{"HostIp": "0.0.0.0", "HostPort": "27017"}, {"HostIp": "::", "HostPort": "27017"}]}, "restart_count": 0}, "hvac-transcription-orchestrator": {"status": "not_found", "needs_rebuild": true}, "hvac-simple-stt": {"status": "running", "health": "unknown", "created": "2025-05-30T16:05:49.832189261Z", "image": "nvidia-stt-polish_simple-stt:latest", "ports": {"8889/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8889"}, {"HostIp": "::", "HostPort": "8889"}]}, "restart_count": 0}, "hvac-email-processor": {"status": "running", "health": "unknown", "created": "2025-05-30T15:53:47.062831631Z", "image": "nvidia-stt-polish_email-processor:latest", "ports": {}, "restart_count": 4788}, "hvac-audio-converter": {"status": "running", "health": "unknown", "created": "2025-05-30T14:59:27.235007915Z", "image": "nvidia-stt-polish_audio-converter:latest", "ports": {}, "restart_count": 4742}, "hvac-gemma-integration": {"status": "running", "health": "unknown", "created": "2025-05-30T14:59:27.234983862Z", "image": "nvidia-stt-polish_gemma-integration:latest", "ports": {}, "restart_count": 4817}}, "service_endpoints": {"weaviate": {"available": false, "response_time": 0.001144, "status_code": 404}, "gospine": {"available": false, "error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1449780f10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "redis_local": {"available": true, "response_time": 0.1, "info": {"redis_version": "7.4.3", "redis_git_sha1": 0, "redis_git_dirty": 0, "redis_build_id": "bcdec76e88a765f1", "redis_mode": "standalone", "os": "Linux **********-microsoft-standard-WSL2 x86_64", "arch_bits": 64, "monotonic_clock": "POSIX clock_gettime", "multiplexing_api": "epoll", "atomicvar_api": "c11-builtin", "gcc_version": "14.2.0", "process_id": 1, "process_supervised": "no", "run_id": "f4364135c060f3f2ffdfe6c3f4f03e58757c902e", "tcp_port": 6379, "server_time_usec": 1748767363937607, "uptime_in_seconds": 6937, "uptime_in_days": 0, "hz": 10, "configured_hz": 10, "lru_clock": 3936899, "executable": "/data/redis-server", "config_file": "", "io_threads_active": 0, "listener0": {"name": "tcp", "bind": "-::*", "port": 6379}, "connected_clients": 1, "cluster_connections": 0, "maxclients": 10000, "client_recent_max_input_buffer": 0, "client_recent_max_output_buffer": 0, "blocked_clients": 0, "tracking_clients": 0, "pubsub_clients": 0, "watching_clients": 0, "clients_in_timeout_table": 0, "total_watched_keys": 0, "total_blocking_keys": 0, "total_blocking_keys_on_nokey": 0, "used_memory": 1114408, "used_memory_human": "1.06M", "used_memory_rss": 13938688, "used_memory_rss_human": "13.29M", "used_memory_peak": 1150272, "used_memory_peak_human": "1.10M", "used_memory_peak_perc": "96.88%", "used_memory_overhead": 947456, "used_memory_startup": 946192, "used_memory_dataset": 166952, "used_memory_dataset_perc": "99.25%", "allocator_allocated": 2283808, "allocator_active": 2506752, "allocator_resident": 7487488, "allocator_muzzy": 0, "total_system_memory": 42020954112, "total_system_memory_human": "39.14G", "used_memory_lua": 31744, "used_memory_vm_eval": 31744, "used_memory_lua_human": "31.00K", "used_memory_scripts_eval": 0, "number_of_cached_scripts": 0, "number_of_functions": 0, "number_of_libraries": 0, "used_memory_vm_functions": 32768, "used_memory_vm_total": 64512, "used_memory_vm_total_human": "63.00K", "used_memory_functions": 192, "used_memory_scripts": 192, "used_memory_scripts_human": "192B", "maxmemory": 0, "maxmemory_human": "0B", "maxmemory_policy": "noeviction", "allocator_frag_ratio": 1.08, "allocator_frag_bytes": 146912, "allocator_rss_ratio": 2.99, "allocator_rss_bytes": 4980736, "rss_overhead_ratio": 1.86, "rss_overhead_bytes": 6451200, "mem_fragmentation_ratio": 12.97, "mem_fragmentation_bytes": 12864352, "mem_not_counted_for_evict": 0, "mem_replication_backlog": 0, "mem_total_replication_buffers": 0, "mem_clients_slaves": 0, "mem_clients_normal": 0, "mem_cluster_links": 0, "mem_aof_buffer": 0, "mem_allocator": "jemalloc-5.3.0", "mem_overhead_db_hashtable_rehashing": 0, "active_defrag_running": 0, "lazyfree_pending_objects": 0, "lazyfreed_objects": 0, "loading": 0, "async_loading": 0, "current_cow_peak": 0, "current_cow_size": 0, "current_cow_size_age": 0, "current_fork_perc": 0.0, "current_save_keys_processed": 0, "current_save_keys_total": 0, "rdb_changes_since_last_save": 0, "rdb_bgsave_in_progress": 0, "rdb_last_save_time": 1748764027, "rdb_last_bgsave_status": "ok", "rdb_last_bgsave_time_sec": 0, "rdb_current_bgsave_time_sec": -1, "rdb_saves": 1, "rdb_last_cow_size": 745472, "rdb_last_load_keys_expired": 0, "rdb_last_load_keys_loaded": 0, "aof_enabled": 0, "aof_rewrite_in_progress": 0, "aof_rewrite_scheduled": 0, "aof_last_rewrite_time_sec": -1, "aof_current_rewrite_time_sec": -1, "aof_last_bgrewrite_status": "ok", "aof_rewrites": 0, "aof_rewrites_consecutive_failures": 0, "aof_last_write_status": "ok", "aof_last_cow_size": 0, "module_fork_in_progress": 0, "module_fork_last_cow_size": 0, "total_connections_received": 11, "total_commands_processed": 32, "instantaneous_ops_per_sec": 0, "total_net_input_bytes": 6508, "total_net_output_bytes": 13021, "total_net_repl_input_bytes": 0, "total_net_repl_output_bytes": 0, "instantaneous_input_kbps": 0.0, "instantaneous_output_kbps": 0.0, "instantaneous_input_repl_kbps": 0.0, "instantaneous_output_repl_kbps": 0.0, "rejected_connections": 0, "sync_full": 0, "sync_partial_ok": 0, "sync_partial_err": 0, "expired_subkeys": 0, "expired_keys": 0, "expired_stale_perc": 0.0, "expired_time_cap_reached_count": 0, "expire_cycle_cpu_milliseconds": 148, "evicted_keys": 0, "evicted_clients": 0, "evicted_scripts": 0, "total_eviction_exceeded_time": 0, "current_eviction_exceeded_time": 0, "keyspace_hits": 3, "keyspace_misses": 0, "pubsub_channels": 0, "pubsub_patterns": 0, "pubsubshard_channels": 0, "latest_fork_usec": 411, "total_forks": 1, "migrate_cached_sockets": 0, "slave_expires_tracked_keys": 0, "active_defrag_hits": 0, "active_defrag_misses": 0, "active_defrag_key_hits": 0, "active_defrag_key_misses": 0, "total_active_defrag_time": 0, "current_active_defrag_time": 0, "tracking_total_keys": 0, "tracking_total_items": 0, "tracking_total_prefixes": 0, "unexpected_error_replies": 0, "total_error_replies": 1, "dump_payload_sanitizations": 0, "total_reads_processed": 43, "total_writes_processed": 32, "io_threaded_reads_processed": 0, "io_threaded_writes_processed": 0, "client_query_buffer_limit_disconnections": 0, "client_output_buffer_limit_disconnections": 0, "reply_buffer_shrinks": 4, "reply_buffer_expands": 0, "eventloop_cycles": 64134, "eventloop_duration_sum": 18762044, "eventloop_duration_cmd_sum": 1837, "instantaneous_eventloop_cycles_per_sec": 9, "instantaneous_eventloop_duration_usec": 303, "acl_access_denied_auth": 0, "acl_access_denied_cmd": 0, "acl_access_denied_key": 0, "acl_access_denied_channel": 0, "role": "master", "connected_slaves": 0, "master_failover_state": "no-failover", "master_replid": "9edb350a92acf64166d0b325a64c11667b34fefc", "master_replid2": 0, "master_repl_offset": 0, "second_repl_offset": -1, "repl_backlog_active": 0, "repl_backlog_size": 1048576, "repl_backlog_first_byte_offset": 0, "repl_backlog_histlen": 0, "used_cpu_sys": 3.606454, "used_cpu_user": 18.054775, "used_cpu_sys_children": 0.002801, "used_cpu_user_children": 0.001124, "used_cpu_sys_main_thread": 3.605669, "used_cpu_user_main_thread": 18.050265, "errorstat_ERR": {"count": 1}, "cluster_enabled": 0, "db0": {"keys": 6, "expires": 6, "avg_ttl": 80348020, "subexpiry": 0}}}, "redis_external": {"available": false, "error": "Error 111 connecting to **************:3037. Connection refused."}, "mongodb": {"available": true, "response_time": 0.002567, "status_code": 200}, "admin_interface": {"available": false, "error": "HTTPConnectionPool(host='localhost', port=7861): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f144940fe10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}}, "weaviate_health": {"/v1/.well-known/live": {"status_code": 200, "response_time": 0.000952, "content_length": 0}, "/v1/.well-known/ready": {"status_code": 200, "response_time": 0.001038, "content_length": 0}, "/v1/meta": {"status_code": 200, "response_time": 0.00575, "content_length": 1894, "meta_data": {"hostname": "http://[::]:8080", "modules": {"backup-filesystem": {"backupsPath": "/var/lib/weaviate/backups"}, "text2vec-transformers": {"model": {"_attn_implementation_autoset": false, "_name_or_path": "sentence-transformers/all-mpnet-base-v2", "add_cross_attention": false, "architectures": ["MPNetForMaskedLM"], "attention_probs_dropout_prob": 0.1, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 0, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 2, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "intermediate_size": 3072, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-05, "length_penalty": 1, "max_length": 20, "max_position_embeddings": 514, "min_length": 0, "model_type": "mpnet", "no_repeat_ngram_size": 0, "num_attention_heads": 12, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 1, "prefix": null, "problem_type": null, "pruned_heads": {}, "relative_attention_num_buckets": 32, "remove_invalid_values": false, "repetition_penalty": 1, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1, "torch_dtype": null, "torchscript": false, "transformers_version": "4.48.2", "typical_p": 1, "use_bfloat16": false, "vocab_size": 30527}, "model_path": "./models/model"}}, "version": "1.25.0"}}, "client_connection": false, "client_error": "Client.__init__() takes 1 positional argument but 2 were given"}, "gospine_integration": {"/api/v1/health": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1445fa9690>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/pipeline/stages": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/pipeline/stages (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1445fab8d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/scoring/criteria": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/scoring/criteria (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1445fadbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/knowledge/suggestions?context=test": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/knowledge/suggestions?context=test (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1445fafad0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}}, "system_resources": {"cpu_percent": 0.9, "memory": {"total": 42020954112, "available": 28122914816, "percent": 33.1}, "disk": {"total": 1081101176832, "free": 873028329472, "percent": 14.9}, "network": {"bytes_sent": 40720697212, "bytes_recv": 38161545852, "packets_sent": 2638444, "packets_recv": 3171875, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}, "processes": 219}, "semantic_framework": {"bridge_import": true, "bridge_creation": true, "semantic_analysis": {"working": true, "processing_time": 4e-06, "confidence": 0.0}}}, "fixes_applied": ["Container hvac-transcription-orchestrator not found - needs creation", "Rebuilt container: hvac-transcription-orchestrator", "Fixed Weaviate connection"], "recommendations": ["Restore services: weaviate, gospine, redis_external, admin_interface", "Fix GoSpine endpoints: /api/v1/health, /api/v1/pipeline/stages, /api/v1/scoring/criteria, /api/v1/knowledge/suggestions?context=test"]}