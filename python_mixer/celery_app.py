#!/usr/bin/env python3
"""
🔄 CELERY APPLICATION FOR HVAC CRM EMAIL PROCESSING
=================================================

Production-ready Celery application for distributed email processing pipeline.
Handles automated email <NAME_EMAIL> and grz<PERSON><PERSON>@koldbringers.pl
with M4A attachment extraction, transcription orchestration, and CRM enrichment.

Features:
- Redis broker with high availability
- Distributed task processing
- Email processing automation
- M4A file extraction and MinIO storage
- Lead scoring and CRM enrichment
- Comprehensive monitoring and logging

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import os
import logging
from celery import Celery
from celery.signals import setup_logging
from kombu import Queue
from datetime import timedelta

# Load configuration
from config_loader import load_config

# Initialize configuration
config = load_config()

# Redis configuration
REDIS_HOST = config.get('redis.host', '**************')
REDIS_PORT = config.get('redis.port', 3037)
REDIS_DB = config.get('redis.db', 0)
REDIS_PASSWORD = config.get('redis.password', '')

# Construct Redis URL
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Create Celery application
celery_app = Celery(
    'hvac_crm_email_processor',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        'tasks.email_processing_tasks',
        'tasks.m4a_extraction_tasks', 
        'tasks.transcription_tasks',
        'tasks.crm_enrichment_tasks',
        'tasks.semantic_analysis_tasks'
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        'tasks.email_processing_tasks.*': {'queue': 'email_processing'},
        'tasks.m4a_extraction_tasks.*': {'queue': 'm4a_extraction'},
        'tasks.transcription_tasks.*': {'queue': 'transcription'},
        'tasks.crm_enrichment_tasks.*': {'queue': 'crm_enrichment'},
        'tasks.semantic_analysis_tasks.*': {'queue': 'semantic_analysis'},
    },
    
    # Queue definitions
    task_queues=(
        Queue('email_processing', routing_key='email_processing'),
        Queue('m4a_extraction', routing_key='m4a_extraction'),
        Queue('transcription', routing_key='transcription'),
        Queue('crm_enrichment', routing_key='crm_enrichment'),
        Queue('semantic_analysis', routing_key='semantic_analysis'),
        Queue('celery', routing_key='celery'),  # Default queue
    ),
    
    # Task execution settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Europe/Warsaw',
    enable_utc=True,
    
    # Task result settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Task retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,

    # Beat schedule for periodic tasks
    beat_schedule={
        'process-dolores-emails': {
            'task': 'tasks.email_processing_tasks.process_dolores_emails',
            'schedule': timedelta(minutes=5),  # Every 5 minutes
        },
        'process-grzegorz-emails': {
            'task': 'tasks.email_processing_tasks.process_grzegorz_emails',
            'schedule': timedelta(minutes=5),  # Every 5 minutes
        },
        'cleanup-old-tasks': {
            'task': 'tasks.maintenance_tasks.cleanup_old_task_results',
            'schedule': timedelta(hours=6),  # Every 6 hours
        },
        'health-check': {
            'task': 'tasks.monitoring_tasks.system_health_check',
            'schedule': timedelta(minutes=2),  # Every 2 minutes
        },
    },
)

# Setup logging
@setup_logging.connect
def config_loggers(*args, **kwargs):
    """Configure logging for Celery workers."""
    from logging.config import dictConfig

    dictConfig({
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'default': {
                'format': '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
            },
            'detailed': {
                'format': '[%(asctime)s: %(levelname)s/%(processName)s/%(name)s] %(message)s',
            },
        },
        'handlers': {
            'console': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'default',
            },
            'file': {
                'level': 'DEBUG',
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': 'logs/celery.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'formatter': 'detailed',
            },
        },
        'root': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
        },
        'loggers': {
            'celery': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False,
            },
            'hvac_crm': {
                'level': 'DEBUG',
                'handlers': ['console', 'file'],
                'propagate': False,
            },
        },
    })

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

if __name__ == '__main__':
    celery_app.start()
