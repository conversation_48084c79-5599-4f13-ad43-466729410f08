#!/usr/bin/env python3
"""
🧠 SEMANTIC ANALYSIS TASKS FOR HVAC CRM
======================================

Celery tasks for semantic analysis using Gobeklitepe framework and Weaviate vector database.
Processes transcriptions and communications for advanced customer intelligence.

Features:
- Gobeklitepe semantic framework integration
- Weaviate vector database operations
- 5 HVAC agent types processing
- 360-degree customer profiling
- Advanced semantic search
- Customer intelligence scoring

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import os
import sys
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from celery import Task
from celery_app import celery_app
from config_loader import load_config
from integrations.gobeklitepe_bridge import GobeklitepeBridge

# Initialize configuration
config = load_config()
logger = logging.getLogger('hvac_crm.semantic_analysis')

class SemanticAnalysisTask(Task):
    """Base task class for semantic analysis with error handling."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"Semantic analysis task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")

@celery_app.task(bind=True, base=SemanticAnalysisTask, max_retries=3)
def analyze_transcription_semantics(self, transcription_result: Dict[str, Any], original_data: Dict[str, Any]):
    """
    Analyze transcription using Gobeklitepe semantic framework.
    
    Args:
        transcription_result: Transcription result data
        original_data: Original transcription task data
    """
    try:
        logger.info("Starting semantic analysis of transcription")
        
        # Initialize Gobeklitepe bridge
        gobeklitepe_bridge = GobeklitepeBridge()
        await gobeklitepe_bridge.initialize()
        
        # Perform semantic analysis
        semantic_result = await gobeklitepe_bridge.analyze_email_semantic(
            email_content=transcription_result.get('transcription', ''),
            customer_id=original_data.get('email_id'),
            email_metadata={
                'source': original_data.get('source'),
                'file_path': original_data.get('file_path'),
                'transcription_service': transcription_result.get('service'),
                'confidence': transcription_result.get('confidence')
            }
        )
        
        # Process with HVAC agents
        agent_responses = await gobeklitepe_bridge.process_with_hvac_agents(
            communication_data={
                'content': transcription_result.get('transcription', ''),
                'type': 'transcription',
                'metadata': original_data.get('metadata', {}),
                'hvac_keywords': transcription_result.get('hvac_keywords', [])
            }
        )
        
        # Create comprehensive analysis
        comprehensive_analysis = {
            'transcription_data': transcription_result,
            'semantic_analysis': semantic_result.__dict__ if hasattr(semantic_result, '__dict__') else semantic_result,
            'agent_responses': [response.__dict__ if hasattr(response, '__dict__') else response for response in agent_responses],
            'customer_intelligence': generate_customer_intelligence(semantic_result, agent_responses),
            'processed_at': datetime.now().isoformat(),
            'original_data': original_data
        }
        
        # Store semantic analysis
        store_semantic_analysis.delay(comprehensive_analysis)
        
        # Update customer profile
        update_customer_profile_semantic.delay(comprehensive_analysis)
        
        # Generate insights
        generate_business_insights.delay(comprehensive_analysis)
        
        logger.info("Semantic analysis completed successfully")
        return comprehensive_analysis
        
    except Exception as exc:
        logger.error(f"Semantic analysis failed: {exc}")
        raise self.retry(exc=exc, countdown=120)

@celery_app.task(bind=True, base=SemanticAnalysisTask, max_retries=3)
def process_with_hvac_agents(self, communication_data: Dict[str, Any]):
    """
    Process communication data with 5 specialized HVAC agents.
    
    Args:
        communication_data: Communication data to process
    """
    try:
        logger.info("Processing with HVAC agents")
        
        # Initialize Gobeklitepe bridge
        gobeklitepe_bridge = GobeklitepeBridge()
        await gobeklitepe_bridge.initialize()
        
        # Process with all agent types
        agent_types = ['conversational', 'analytical', 'decision_making', 'integration', 'optimization']
        agent_responses = await gobeklitepe_bridge.process_with_hvac_agents(
            communication_data=communication_data,
            agent_types=agent_types
        )
        
        # Create agent processing result
        agent_result = {
            'communication_data': communication_data,
            'agent_responses': [response.__dict__ if hasattr(response, '__dict__') else response for response in agent_responses],
            'processing_summary': create_agent_processing_summary(agent_responses),
            'processed_at': datetime.now().isoformat()
        }
        
        # Store agent results
        store_agent_results.delay(agent_result)
        
        logger.info(f"HVAC agents processing completed: {len(agent_responses)} agents")
        return agent_result
        
    except Exception as exc:
        logger.error(f"HVAC agents processing failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

@celery_app.task(bind=True, base=SemanticAnalysisTask, max_retries=3)
def create_360_customer_profile(self, customer_id: str, data_sources: List[Dict[str, Any]]):
    """
    Create comprehensive 360-degree customer profile.
    
    Args:
        customer_id: Customer identifier
        data_sources: List of data sources (emails, transcriptions, service data, etc.)
    """
    try:
        logger.info(f"Creating 360-degree customer profile for {customer_id}")
        
        # Aggregate data from all sources
        aggregated_data = aggregate_customer_data(data_sources)
        
        # Generate customer intelligence
        customer_intelligence = generate_comprehensive_customer_intelligence(aggregated_data)
        
        # Create unified profile
        unified_profile = {
            'customer_id': customer_id,
            'profile_completeness': calculate_profile_completeness(aggregated_data),
            'aggregated_data': aggregated_data,
            'customer_intelligence': customer_intelligence,
            'health_score': calculate_customer_health_score(aggregated_data),
            'business_value': calculate_business_value(aggregated_data),
            'next_actions': generate_next_actions(customer_intelligence),
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat()
        }
        
        # Store unified profile
        store_unified_customer_profile.delay(unified_profile)
        
        # Update Weaviate vector database
        update_weaviate_customer_profile.delay(unified_profile)
        
        logger.info(f"360-degree customer profile created: {unified_profile['profile_completeness']:.1f}% complete")
        return unified_profile
        
    except Exception as exc:
        logger.error(f"360-degree customer profile creation failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

def generate_customer_intelligence(semantic_result, agent_responses) -> Dict[str, Any]:
    """Generate customer intelligence from semantic analysis and agent responses."""
    try:
        # Extract insights from semantic analysis
        sentiment = getattr(semantic_result, 'sentiment', 'neutral')
        urgency_score = getattr(semantic_result, 'urgency_score', 0.5)
        equipment_mentioned = getattr(semantic_result, 'equipment_mentioned', [])
        
        # Extract insights from agent responses
        agent_insights = {}
        for response in agent_responses:
            agent_type = getattr(response, 'agent_type', 'unknown')
            recommendations = getattr(response, 'recommendations', [])
            confidence = getattr(response, 'confidence', 0.0)
            
            agent_insights[agent_type] = {
                'recommendations': recommendations,
                'confidence': confidence
            }
        
        # Generate overall intelligence
        intelligence = {
            'sentiment_analysis': {
                'sentiment': sentiment,
                'urgency_score': urgency_score
            },
            'equipment_analysis': {
                'mentioned_equipment': equipment_mentioned,
                'equipment_count': len(equipment_mentioned)
            },
            'agent_insights': agent_insights,
            'overall_confidence': sum(getattr(r, 'confidence', 0) for r in agent_responses) / len(agent_responses) if agent_responses else 0,
            'intelligence_score': calculate_intelligence_score(semantic_result, agent_responses)
        }
        
        return intelligence
        
    except Exception as e:
        logger.error(f"Error generating customer intelligence: {e}")
        return {}

def create_agent_processing_summary(agent_responses) -> Dict[str, Any]:
    """Create summary of agent processing results."""
    try:
        summary = {
            'total_agents': len(agent_responses),
            'agent_types': [getattr(r, 'agent_type', 'unknown') for r in agent_responses],
            'average_confidence': sum(getattr(r, 'confidence', 0) for r in agent_responses) / len(agent_responses) if agent_responses else 0,
            'total_recommendations': sum(len(getattr(r, 'recommendations', [])) for r in agent_responses),
            'processing_success': True
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Error creating agent processing summary: {e}")
        return {'processing_success': False, 'error': str(e)}

def aggregate_customer_data(data_sources: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Aggregate customer data from multiple sources."""
    try:
        aggregated = {
            'emails': [],
            'transcriptions': [],
            'service_orders': [],
            'equipment': [],
            'financial_data': [],
            'communication_history': [],
            'total_interactions': 0
        }
        
        for source in data_sources:
            source_type = source.get('type', 'unknown')
            
            if source_type == 'email':
                aggregated['emails'].append(source)
            elif source_type == 'transcription':
                aggregated['transcriptions'].append(source)
            elif source_type == 'service_order':
                aggregated['service_orders'].append(source)
            elif source_type == 'equipment':
                aggregated['equipment'].append(source)
            elif source_type == 'financial':
                aggregated['financial_data'].append(source)
            
            aggregated['communication_history'].append(source)
            aggregated['total_interactions'] += 1
        
        return aggregated
        
    except Exception as e:
        logger.error(f"Error aggregating customer data: {e}")
        return {}

def generate_comprehensive_customer_intelligence(aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
    """Generate comprehensive customer intelligence."""
    try:
        intelligence = {
            'communication_patterns': analyze_communication_patterns(aggregated_data),
            'service_history': analyze_service_history(aggregated_data),
            'equipment_profile': analyze_equipment_profile(aggregated_data),
            'financial_profile': analyze_financial_profile(aggregated_data),
            'predictive_insights': generate_predictive_insights(aggregated_data),
            'risk_assessment': assess_customer_risk(aggregated_data),
            'opportunity_analysis': analyze_opportunities(aggregated_data)
        }
        
        return intelligence
        
    except Exception as e:
        logger.error(f"Error generating comprehensive customer intelligence: {e}")
        return {}

def calculate_profile_completeness(aggregated_data: Dict[str, Any]) -> float:
    """Calculate customer profile completeness percentage."""
    try:
        completeness_factors = {
            'basic_info': 20,  # Name, email, phone
            'communication_history': 15,  # Email/call history
            'service_history': 20,  # Service orders
            'equipment_data': 20,  # Equipment information
            'financial_data': 15,  # Payment history
            'preferences': 10   # Customer preferences
        }
        
        score = 0
        
        # Basic info (simplified check)
        if aggregated_data.get('emails'):
            score += completeness_factors['basic_info']
        
        # Communication history
        if aggregated_data.get('total_interactions', 0) > 0:
            score += completeness_factors['communication_history']
        
        # Service history
        if aggregated_data.get('service_orders'):
            score += completeness_factors['service_history']
        
        # Equipment data
        if aggregated_data.get('equipment'):
            score += completeness_factors['equipment_data']
        
        # Financial data
        if aggregated_data.get('financial_data'):
            score += completeness_factors['financial_data']
        
        return min(score, 100.0)
        
    except Exception as e:
        logger.error(f"Error calculating profile completeness: {e}")
        return 0.0

def calculate_customer_health_score(aggregated_data: Dict[str, Any]) -> float:
    """Calculate customer health score."""
    try:
        # Simplified health score calculation
        base_score = 50.0
        
        # Positive factors
        if aggregated_data.get('total_interactions', 0) > 5:
            base_score += 20
        
        if aggregated_data.get('service_orders'):
            base_score += 15
        
        if aggregated_data.get('financial_data'):
            base_score += 15
        
        return min(base_score, 100.0)
        
    except Exception as e:
        logger.error(f"Error calculating customer health score: {e}")
        return 50.0

@celery_app.task(bind=True, base=SemanticAnalysisTask)
def store_semantic_analysis(self, analysis_data: Dict[str, Any]):
    """Store semantic analysis results."""
    try:
        # Store in Redis for now (would be database in production)
        import redis
        
        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )
        
        key = f"hvac_crm:semantic_analysis:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        redis_client.setex(key, timedelta(days=30).total_seconds(), json.dumps(analysis_data))
        
        logger.info(f"Semantic analysis stored: {key}")
        return True
        
    except Exception as e:
        logger.error(f"Error storing semantic analysis: {e}")
        return False
