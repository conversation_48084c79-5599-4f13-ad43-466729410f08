#!/usr/bin/env python3
"""
🎵 M4A EXTRACTION TASKS FOR HVAC CRM
===================================

Celery tasks for automated M4A file extraction, processing, and storage.
Handles attachment extraction from emails, MinIO storage, and transcription queue population.

Features:
- Automated M4A attachment extraction
- MinIO storage with metadata
- File validation and processing
- Transcription queue population
- Comprehensive error handling
- File deduplication

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import os
import sys
import logging
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import tempfile

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from celery import Task
from celery_app import celery_app
from config_loader import load_config
from storage.enhanced_minio_manager import EnhancedMinIOManager

# Initialize configuration
config = load_config()
logger = logging.getLogger('hvac_crm.m4a_extraction')

class M4AExtractionTask(Task):
    """Base task class for M4A extraction with error handling."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"M4A extraction task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")

@celery_app.task(bind=True, base=M4AExtractionTask, max_retries=3)
def extract_m4a_attachment(self, email_id: str, attachment_data: Dict[str, Any], source: str):
    """
    Extract M4A attachment from email and store in MinIO.
    
    Args:
        email_id: Email message ID
        attachment_data: Attachment data dictionary
        source: Email source (dolores/grzegorz)
    """
    try:
        logger.info(f"Extracting M4A attachment from email {email_id}")
        
        # Initialize MinIO manager
        minio_manager = EnhancedMinIOManager()
        
        # Get attachment details
        filename = attachment_data.get('filename', 'unknown.m4a')
        content = attachment_data.get('content', b'')
        content_type = attachment_data.get('content_type', 'audio/m4a')
        
        # Validate M4A file
        if not validate_m4a_file(content, filename):
            raise ValueError(f"Invalid M4A file: {filename}")
        
        # Generate file hash for deduplication
        file_hash = hashlib.sha256(content).hexdigest()
        
        # Check if file already exists
        existing_file = check_file_exists(minio_manager, file_hash)
        if existing_file:
            logger.info(f"M4A file already exists: {existing_file}")
            # Still queue for transcription if not already processed
            queue_for_transcription.delay(
                file_path=existing_file,
                email_id=email_id,
                source=source,
                metadata=attachment_data
            )
            return existing_file
        
        # Create unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_filename = f"{source}_{timestamp}_{file_hash[:8]}_{filename}"
        
        # Store in MinIO
        bucket_name = config.get('minio.buckets.audio', 'hvac-audio-files')
        object_name = f"{source}/{datetime.now().strftime('%Y/%m/%d')}/{unique_filename}"
        
        # Create metadata
        metadata = {
            'email_id': email_id,
            'source': source,
            'original_filename': filename,
            'file_hash': file_hash,
            'content_type': content_type,
            'extracted_at': datetime.now().isoformat(),
            'file_size': len(content),
            'processing_status': 'extracted'
        }
        
        # Upload to MinIO
        with tempfile.NamedTemporaryFile() as temp_file:
            temp_file.write(content)
            temp_file.flush()
            
            upload_result = minio_manager.upload_file(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=temp_file.name,
                metadata=metadata
            )
        
        if upload_result['success']:
            file_path = f"{bucket_name}/{object_name}"
            logger.info(f"M4A file uploaded successfully: {file_path}")
            
            # Queue for transcription
            queue_for_transcription.delay(
                file_path=file_path,
                email_id=email_id,
                source=source,
                metadata=metadata
            )
            
            # Update file registry
            update_file_registry.delay(
                file_path=file_path,
                file_hash=file_hash,
                metadata=metadata
            )
            
            return file_path
        else:
            raise Exception(f"Failed to upload M4A file: {upload_result.get('error')}")
            
    except Exception as exc:
        logger.error(f"M4A extraction failed for email {email_id}: {exc}")
        raise self.retry(exc=exc, countdown=60)

@celery_app.task(bind=True, base=M4AExtractionTask)
def queue_for_transcription(self, file_path: str, email_id: str, source: str, metadata: Dict[str, Any]):
    """
    Queue M4A file for transcription processing.
    
    Args:
        file_path: MinIO file path
        email_id: Email message ID
        source: Email source
        metadata: File metadata
    """
    try:
        from .transcription_tasks import process_transcription
        
        # Create transcription task data
        transcription_data = {
            'file_path': file_path,
            'email_id': email_id,
            'source': source,
            'priority': 'high' if source == 'dolores' else 'medium',
            'language': 'polish',
            'domain': 'hvac',
            'metadata': metadata,
            'queued_at': datetime.now().isoformat()
        }
        
        # Queue for transcription
        process_transcription.delay(transcription_data)
        
        logger.info(f"M4A file queued for transcription: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to queue file for transcription: {e}")
        return False

@celery_app.task(bind=True, base=M4AExtractionTask)
def update_file_registry(self, file_path: str, file_hash: str, metadata: Dict[str, Any]):
    """
    Update file registry for deduplication and tracking.
    
    Args:
        file_path: MinIO file path
        file_hash: File hash for deduplication
        metadata: File metadata
    """
    try:
        # This would typically update a database or Redis registry
        # For now, we'll use Redis for simple tracking
        import redis
        
        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )
        
        # Store file registry entry
        registry_key = f"hvac_crm:file_registry:{file_hash}"
        registry_data = {
            'file_path': file_path,
            'metadata': metadata,
            'registered_at': datetime.now().isoformat()
        }
        
        redis_client.setex(
            registry_key,
            timedelta(days=365).total_seconds(),  # Keep for 1 year
            json.dumps(registry_data)
        )
        
        logger.info(f"File registry updated for hash: {file_hash}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to update file registry: {e}")
        return False

def validate_m4a_file(content: bytes, filename: str) -> bool:
    """
    Validate M4A file content and format.
    
    Args:
        content: File content bytes
        filename: Original filename
        
    Returns:
        True if valid M4A file, False otherwise
    """
    try:
        # Check file extension
        if not filename.lower().endswith('.m4a'):
            return False
        
        # Check minimum file size (1KB)
        if len(content) < 1024:
            return False
        
        # Check M4A file signature (basic check)
        # M4A files typically start with 'ftyp' at offset 4
        if len(content) >= 8:
            if content[4:8] == b'ftyp':
                return True
        
        # Additional MIME type check
        mime_type, _ = mimetypes.guess_type(filename)
        if mime_type in ['audio/m4a', 'audio/mp4', 'audio/x-m4a']:
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error validating M4A file: {e}")
        return False

def check_file_exists(minio_manager: EnhancedMinIOManager, file_hash: str) -> Optional[str]:
    """
    Check if file with given hash already exists in registry.
    
    Args:
        minio_manager: MinIO manager instance
        file_hash: File hash to check
        
    Returns:
        Existing file path if found, None otherwise
    """
    try:
        import redis
        import json
        
        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )
        
        registry_key = f"hvac_crm:file_registry:{file_hash}"
        registry_data = redis_client.get(registry_key)
        
        if registry_data:
            data = json.loads(registry_data)
            return data.get('file_path')
        
        return None
        
    except Exception as e:
        logger.error(f"Error checking file existence: {e}")
        return None
