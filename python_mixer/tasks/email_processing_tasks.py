#!/usr/bin/env python3
"""
📧 EMAIL PROCESSING TASKS FOR HVAC CRM
=====================================

Celery tasks for automated email <NAME_EMAIL> and grz<PERSON><EMAIL>.
Handles email fetching, parsing, attachment extraction, and initial processing.

Features:
- Automated email fetching with IMAP
- M4A attachment detection and extraction
- Email content analysis and categorization
- Lead scoring and customer identification
- Integration with MinIO for file storage
- Comprehensive error handling and retry logic

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import os
import sys
import logging
import email
import imaplib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from celery import Task
from celery_app import celery_app
from config_loader import load_config
from email_processing.imap_client import IMAPClient
from email_processing.email_parser import EmailParser
from storage.enhanced_minio_manager import EnhancedMinIOManager

# Initialize configuration
config = load_config()
logger = logging.getLogger('hvac_crm.email_processing')

class EmailProcessingTask(Task):
    """Base task class for email processing with error handling."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"Task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")

@celery_app.task(bind=True, base=EmailProcessingTask, max_retries=3)
def process_dolores_emails(self):
    """
    Process <NAME_EMAIL> account.
    Focuses on M4A transcription attachments.
    """
    try:
        logger.info("Starting Dolores email processing...")
        
        # Initialize email client
        dolores_config = config.get('email.dolores', {})
        imap_client = IMAPClient(
            host=dolores_config.get('host'),
            port=dolores_config.get('port', 993),
            username=dolores_config.get('username'),
            password=os.getenv('DOLORES_EMAIL_PASSWORD'),
            use_ssl=dolores_config.get('use_ssl', True)
        )
        
        # Connect and fetch recent emails
        with imap_client:
            # Get emails from last 24 hours
            since_date = datetime.now() - timedelta(days=1)
            emails = imap_client.fetch_emails_since(since_date, limit=50)
            
            processed_count = 0
            m4a_count = 0
            
            for email_data in emails:
                try:
                    # Parse email
                    parser = EmailParser()
                    parsed_email = parser.parse_email(email_data)
                    
                    # Check for M4A attachments
                    m4a_attachments = [
                        att for att in parsed_email.get('attachments', [])
                        if att.get('filename', '').lower().endswith('.m4a')
                    ]
                    
                    if m4a_attachments:
                        # Process M4A attachments
                        for attachment in m4a_attachments:
                            extract_m4a_attachment.delay(
                                email_id=parsed_email.get('message_id'),
                                attachment_data=attachment,
                                source='dolores'
                            )
                            m4a_count += 1
                    
                    # Enrich CRM data
                    enrich_customer_data.delay(
                        email_data=parsed_email,
                        source='dolores'
                    )
                    
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing individual email: {e}")
                    continue
            
            logger.info(f"Dolores email processing completed: {processed_count} emails, {m4a_count} M4A files")
            return {
                'processed_emails': processed_count,
                'm4a_attachments': m4a_count,
                'source': 'dolores',
                'timestamp': datetime.now().isoformat()
            }
            
    except Exception as exc:
        logger.error(f"Dolores email processing failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

@celery_app.task(bind=True, base=EmailProcessingTask, max_retries=3)
def process_grzegorz_emails(self):
    """
    Process <NAME_EMAIL> account.
    Focuses on customer communications and service requests.
    """
    try:
        logger.info("Starting Grzegorz email processing...")
        
        # Initialize email client
        grzegorz_config = config.get('email.grzegorz', {})
        imap_client = IMAPClient(
            host=grzegorz_config.get('host'),
            port=grzegorz_config.get('port', 993),
            username=grzegorz_config.get('username'),
            password=os.getenv('GRZEGORZ_EMAIL_PASSWORD'),
            use_ssl=grzegorz_config.get('use_ssl', True)
        )
        
        # Connect and fetch recent emails
        with imap_client:
            # Get emails from last 24 hours
            since_date = datetime.now() - timedelta(days=1)
            emails = imap_client.fetch_emails_since(since_date, limit=100)
            
            processed_count = 0
            customer_emails = 0
            
            for email_data in emails:
                try:
                    # Parse email
                    parser = EmailParser()
                    parsed_email = parser.parse_email(email_data)
                    
                    # Categorize email type
                    email_category = categorize_email_content(parsed_email.get('body', ''))
                    
                    if email_category in ['customer_inquiry', 'service_request', 'complaint']:
                        customer_emails += 1
                        
                        # Process customer communication
                        process_customer_communication.delay(
                            email_data=parsed_email,
                            category=email_category,
                            source='grzegorz'
                        )
                    
                    # Enrich CRM data
                    enrich_customer_data.delay(
                        email_data=parsed_email,
                        source='grzegorz'
                    )
                    
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing individual email: {e}")
                    continue
            
            logger.info(f"Grzegorz email processing completed: {processed_count} emails, {customer_emails} customer communications")
            return {
                'processed_emails': processed_count,
                'customer_communications': customer_emails,
                'source': 'grzegorz',
                'timestamp': datetime.now().isoformat()
            }
            
    except Exception as exc:
        logger.error(f"Grzegorz email processing failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

def categorize_email_content(email_body: str) -> str:
    """
    Categorize email content based on keywords and patterns.
    
    Args:
        email_body: Email body text
        
    Returns:
        Email category string
    """
    email_body_lower = email_body.lower()
    
    # HVAC service keywords
    service_keywords = ['serwis', 'naprawa', 'awaria', 'klimatyzacja', 'montaż', 'instalacja']
    complaint_keywords = ['reklamacja', 'problem', 'nie działa', 'zepsuty', 'awaria']
    inquiry_keywords = ['zapytanie', 'oferta', 'wycena', 'informacja', 'pytanie']
    
    if any(keyword in email_body_lower for keyword in complaint_keywords):
        return 'complaint'
    elif any(keyword in email_body_lower for keyword in service_keywords):
        return 'service_request'
    elif any(keyword in email_body_lower for keyword in inquiry_keywords):
        return 'customer_inquiry'
    else:
        return 'general'

# Import other task functions
from .m4a_extraction_tasks import extract_m4a_attachment
from .crm_enrichment_tasks import enrich_customer_data, process_customer_communication
