#!/usr/bin/env python3
"""
💼 CRM ENRICHMENT TASKS FOR HVAC CRM
===================================

Celery tasks for automated CRM data enrichment and lead scoring.
Processes email communications to enhance customer profiles and generate business intelligence.

Features:
- Automated lead scoring algorithms
- Customer profile enrichment
- Communication categorization
- Business intelligence generation
- GoSpine API integration
- Comprehensive analytics

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import os
import sys
import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from celery import Task
from celery_app import celery_app
from config_loader import load_config

# Initialize configuration
config = load_config()
logger = logging.getLogger('hvac_crm.crm_enrichment')

class CRMEnrichmentTask(Task):
    """Base task class for CRM enrichment with error handling."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"CRM enrichment task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")

@celery_app.task(bind=True, base=CRMEnrichmentTask, max_retries=3)
def enrich_customer_data(self, email_data: Dict[str, Any], source: str):
    """
    Enrich customer data based on email communication.
    
    Args:
        email_data: Parsed email data
        source: Email source (dolores/grzegorz)
    """
    try:
        logger.info(f"Enriching customer data from {source} email")
        
        # Extract customer information
        customer_info = extract_customer_info(email_data)
        
        # Calculate lead score
        lead_score = calculate_lead_score(email_data, customer_info)
        
        # Categorize communication
        communication_category = categorize_communication(email_data)
        
        # Extract business intelligence
        business_insights = extract_business_insights(email_data, customer_info)
        
        # Create enrichment data
        enrichment_data = {
            'customer_info': customer_info,
            'lead_score': lead_score,
            'communication_category': communication_category,
            'business_insights': business_insights,
            'source': source,
            'processed_at': datetime.now().isoformat(),
            'email_metadata': {
                'message_id': email_data.get('message_id'),
                'subject': email_data.get('subject'),
                'from': email_data.get('from'),
                'date': email_data.get('date')
            }
        }
        
        # Store enrichment data
        store_enrichment_data.delay(enrichment_data)
        
        # Update customer profile
        update_customer_profile.delay(customer_info, enrichment_data)
        
        # Generate alerts if needed
        if lead_score > 80 or communication_category == 'urgent':
            generate_alert.delay(enrichment_data)
        
        logger.info(f"Customer data enrichment completed: Lead score {lead_score}")
        return enrichment_data
        
    except Exception as exc:
        logger.error(f"Customer data enrichment failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

@celery_app.task(bind=True, base=CRMEnrichmentTask, max_retries=3)
def process_customer_communication(self, email_data: Dict[str, Any], category: str, source: str):
    """
    Process customer communication for automated responses and workflows.
    
    Args:
        email_data: Parsed email data
        category: Communication category
        source: Email source
    """
    try:
        logger.info(f"Processing customer communication: {category}")
        
        # Analyze communication sentiment
        sentiment_analysis = analyze_communication_sentiment(email_data)
        
        # Extract service requirements
        service_requirements = extract_service_requirements(email_data)
        
        # Generate automated response suggestions
        response_suggestions = generate_response_suggestions(email_data, category)
        
        # Create workflow tasks
        workflow_tasks = create_workflow_tasks(email_data, category, service_requirements)
        
        # Process communication data
        communication_data = {
            'email_data': email_data,
            'category': category,
            'source': source,
            'sentiment_analysis': sentiment_analysis,
            'service_requirements': service_requirements,
            'response_suggestions': response_suggestions,
            'workflow_tasks': workflow_tasks,
            'processed_at': datetime.now().isoformat()
        }
        
        # Store communication data
        store_communication_data.delay(communication_data)
        
        # Execute workflow tasks
        for task in workflow_tasks:
            execute_workflow_task.delay(task, communication_data)
        
        logger.info(f"Customer communication processing completed: {len(workflow_tasks)} tasks created")
        return communication_data
        
    except Exception as exc:
        logger.error(f"Customer communication processing failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

def extract_customer_info(email_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract customer information from email data."""
    try:
        from_address = email_data.get('from', '')
        body = email_data.get('body', '')
        subject = email_data.get('subject', '')
        
        # Extract email address and name
        email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', from_address)
        email_address = email_match.group(0) if email_match else ''
        
        # Extract name (simple heuristic)
        name_match = re.search(r'^([^<]+)', from_address)
        name = name_match.group(1).strip() if name_match else ''
        
        # Extract phone number from body
        phone_pattern = r'(?:\+48\s?)?(?:\d{3}[-\s]?\d{3}[-\s]?\d{3}|\d{2}[-\s]?\d{3}[-\s]?\d{2}[-\s]?\d{2})'
        phone_match = re.search(phone_pattern, body)
        phone = phone_match.group(0) if phone_match else ''
        
        # Extract address information
        address_keywords = ['warszawa', 'kraków', 'gdańsk', 'wrocław', 'poznań', 'łódź']
        location = ''
        for keyword in address_keywords:
            if keyword in body.lower():
                location = keyword.title()
                break
        
        return {
            'email': email_address,
            'name': name,
            'phone': phone,
            'location': location,
            'first_contact': datetime.now().isoformat(),
            'communication_count': 1
        }
        
    except Exception as e:
        logger.error(f"Error extracting customer info: {e}")
        return {}

def calculate_lead_score(email_data: Dict[str, Any], customer_info: Dict[str, Any]) -> int:
    """Calculate lead score based on email content and customer information."""
    try:
        score = 0
        body = email_data.get('body', '').lower()
        subject = email_data.get('subject', '').lower()
        
        # High-value keywords
        high_value_keywords = ['montaż', 'instalacja', 'nowy', 'budowa', 'remont']
        medium_value_keywords = ['serwis', 'naprawa', 'konserwacja', 'przegląd']
        urgent_keywords = ['pilne', 'awaria', 'nie działa', 'problem']
        
        # Score based on keywords
        for keyword in high_value_keywords:
            if keyword in body or keyword in subject:
                score += 20
        
        for keyword in medium_value_keywords:
            if keyword in body or keyword in subject:
                score += 10
        
        for keyword in urgent_keywords:
            if keyword in body or keyword in subject:
                score += 15
        
        # Score based on customer information
        if customer_info.get('phone'):
            score += 10  # Phone number provided
        
        if customer_info.get('location'):
            score += 5   # Location provided
        
        # Score based on email characteristics
        if len(body) > 100:
            score += 5   # Detailed inquiry
        
        if 'oferta' in body or 'wycena' in subject:
            score += 25  # Request for quote
        
        # Equipment mentions
        equipment_keywords = ['klimatyzacja', 'split', 'multi', 'vrf', 'daikin', 'lg']
        equipment_mentions = sum(1 for keyword in equipment_keywords if keyword in body)
        score += equipment_mentions * 5
        
        return min(score, 100)  # Cap at 100
        
    except Exception as e:
        logger.error(f"Error calculating lead score: {e}")
        return 0

def categorize_communication(email_data: Dict[str, Any]) -> str:
    """Categorize communication type."""
    try:
        body = email_data.get('body', '').lower()
        subject = email_data.get('subject', '').lower()
        
        # Urgent categories
        if any(keyword in body or keyword in subject for keyword in ['pilne', 'awaria', 'nie działa']):
            return 'urgent'
        
        # Service request
        if any(keyword in body or keyword in subject for keyword in ['serwis', 'naprawa', 'problem']):
            return 'service_request'
        
        # Sales inquiry
        if any(keyword in body or keyword in subject for keyword in ['oferta', 'wycena', 'montaż', 'instalacja']):
            return 'sales_inquiry'
        
        # General inquiry
        if any(keyword in body or keyword in subject for keyword in ['pytanie', 'informacja', 'zapytanie']):
            return 'general_inquiry'
        
        return 'general'
        
    except Exception as e:
        logger.error(f"Error categorizing communication: {e}")
        return 'general'

def analyze_communication_sentiment(email_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze sentiment of customer communication."""
    try:
        body = email_data.get('body', '').lower()
        
        # Simple sentiment analysis
        positive_words = ['dziękuję', 'świetnie', 'doskonale', 'polecam', 'zadowolony']
        negative_words = ['problem', 'awaria', 'nie działa', 'zły', 'niezadowolony', 'reklamacja']
        neutral_words = ['pytanie', 'informacja', 'zapytanie']
        
        positive_count = sum(1 for word in positive_words if word in body)
        negative_count = sum(1 for word in negative_words if word in body)
        neutral_count = sum(1 for word in neutral_words if word in body)
        
        if negative_count > positive_count:
            sentiment = 'negative'
            confidence = min(negative_count / (positive_count + negative_count + 1), 0.9)
        elif positive_count > negative_count:
            sentiment = 'positive'
            confidence = min(positive_count / (positive_count + negative_count + 1), 0.9)
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'positive_indicators': positive_count,
            'negative_indicators': negative_count,
            'neutral_indicators': neutral_count
        }
        
    except Exception as e:
        logger.error(f"Error analyzing sentiment: {e}")
        return {'sentiment': 'neutral', 'confidence': 0.5}

def extract_service_requirements(email_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract service requirements from email content."""
    try:
        body = email_data.get('body', '').lower()
        
        # Equipment type
        equipment_types = {
            'split': ['split', 'klimatyzator ścienny'],
            'multi_split': ['multi split', 'multi-split'],
            'vrf': ['vrf', 'vrv'],
            'centrala': ['centrala', 'klimatyzacja centralna']
        }
        
        detected_equipment = []
        for eq_type, keywords in equipment_types.items():
            if any(keyword in body for keyword in keywords):
                detected_equipment.append(eq_type)
        
        # Service type
        service_types = {
            'installation': ['montaż', 'instalacja'],
            'maintenance': ['serwis', 'konserwacja', 'przegląd'],
            'repair': ['naprawa', 'awaria', 'usterka'],
            'consultation': ['doradztwo', 'konsultacja', 'wycena']
        }
        
        detected_services = []
        for service_type, keywords in service_types.items():
            if any(keyword in body for keyword in keywords):
                detected_services.append(service_type)
        
        # Urgency level
        urgency_keywords = ['pilne', 'natychmiast', 'szybko', 'awaria']
        urgency_level = 'high' if any(keyword in body for keyword in urgency_keywords) else 'normal'
        
        return {
            'equipment_types': detected_equipment,
            'service_types': detected_services,
            'urgency_level': urgency_level,
            'estimated_complexity': 'high' if len(detected_equipment) > 1 else 'medium'
        }
        
    except Exception as e:
        logger.error(f"Error extracting service requirements: {e}")
        return {}

def extract_business_insights(email_data: Dict[str, Any], customer_info: Dict[str, Any]) -> Dict[str, Any]:
    """Extract business intelligence insights."""
    try:
        body = email_data.get('body', '').lower()
        
        # Market segment
        residential_keywords = ['dom', 'mieszkanie', 'prywatny']
        commercial_keywords = ['biuro', 'firma', 'sklep', 'restauracja', 'hotel']
        
        if any(keyword in body for keyword in commercial_keywords):
            market_segment = 'commercial'
        elif any(keyword in body for keyword in residential_keywords):
            market_segment = 'residential'
        else:
            market_segment = 'unknown'
        
        # Budget indicators
        budget_keywords = ['budżet', 'koszt', 'cena', 'ile kosztuje']
        budget_conscious = any(keyword in body for keyword in budget_keywords)
        
        # Competition mentions
        competitor_keywords = ['konkurencja', 'inna firma', 'porównanie']
        competition_aware = any(keyword in body for keyword in competitor_keywords)
        
        return {
            'market_segment': market_segment,
            'budget_conscious': budget_conscious,
            'competition_aware': competition_aware,
            'communication_quality': 'high' if len(body) > 200 else 'medium',
            'technical_knowledge': 'high' if any(tech in body for tech in ['r32', 'cop', 'scop', 'inverter']) else 'low'
        }
        
    except Exception as e:
        logger.error(f"Error extracting business insights: {e}")
        return {}

@celery_app.task(bind=True, base=CRMEnrichmentTask)
def store_enrichment_data(self, enrichment_data: Dict[str, Any]):
    """Store enrichment data in database."""
    try:
        # This would store in actual database
        # For now, we'll use Redis for temporary storage
        import redis
        
        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )
        
        # Store enrichment data
        key = f"hvac_crm:enrichment:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        redis_client.setex(key, timedelta(days=30).total_seconds(), json.dumps(enrichment_data))
        
        logger.info(f"Enrichment data stored: {key}")
        return True

    except Exception as e:
        logger.error(f"Error storing enrichment data: {e}")
        return False

def generate_response_suggestions(email_data: Dict[str, Any], category: str) -> List[str]:
    """Generate automated response suggestions."""
    try:
        suggestions = []

        if category == 'urgent':
            suggestions.extend([
                "Dziękujemy za pilne zgłoszenie. Nasz technik skontaktuje się w ciągu 2 godzin.",
                "Rozumiemy pilność sytuacji. Organizujemy natychmiastową wizytę serwisową."
            ])
        elif category == 'service_request':
            suggestions.extend([
                "Dziękujemy za zgłoszenie serwisowe. Skontaktujemy się w ciągu 24 godzin.",
                "Przygotujemy wycenę serwisu i prześlemy w ciągu 1 dnia roboczego."
            ])
        elif category == 'sales_inquiry':
            suggestions.extend([
                "Dziękujemy za zainteresowanie naszymi usługami. Przygotujemy ofertę.",
                "Skontaktuje się z Państwem nasz doradca w celu omówienia szczegółów."
            ])

        return suggestions

    except Exception as e:
        logger.error(f"Error generating response suggestions: {e}")
        return []

def create_workflow_tasks(email_data: Dict[str, Any], category: str, service_requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Create workflow tasks based on communication."""
    try:
        tasks = []

        # Always create a follow-up task
        tasks.append({
            'type': 'follow_up',
            'priority': 'high' if category == 'urgent' else 'medium',
            'due_date': (datetime.now() + timedelta(hours=2 if category == 'urgent' else 24)).isoformat(),
            'description': f"Follow up on {category} communication",
            'assigned_to': 'customer_service'
        })

        # Service-specific tasks
        if 'installation' in service_requirements.get('service_types', []):
            tasks.append({
                'type': 'site_survey',
                'priority': 'medium',
                'due_date': (datetime.now() + timedelta(days=3)).isoformat(),
                'description': "Schedule site survey for installation",
                'assigned_to': 'technical_team'
            })

        if 'repair' in service_requirements.get('service_types', []):
            tasks.append({
                'type': 'service_appointment',
                'priority': 'high' if service_requirements.get('urgency_level') == 'high' else 'medium',
                'due_date': (datetime.now() + timedelta(hours=4 if service_requirements.get('urgency_level') == 'high' else 48)).isoformat(),
                'description': "Schedule repair service appointment",
                'assigned_to': 'service_team'
            })

        return tasks

    except Exception as e:
        logger.error(f"Error creating workflow tasks: {e}")
        return []

@celery_app.task(bind=True, base=CRMEnrichmentTask)
def update_customer_profile(self, customer_info: Dict[str, Any], enrichment_data: Dict[str, Any]):
    """Update customer profile with enrichment data."""
    try:
        # This would update the actual customer database
        # For now, we'll use Redis for storage
        import redis

        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )

        customer_email = customer_info.get('email', 'unknown')
        profile_key = f"hvac_crm:customer_profile:{customer_email}"

        # Get existing profile or create new
        existing_profile = redis_client.get(profile_key)
        if existing_profile:
            profile = json.loads(existing_profile)
        else:
            profile = customer_info.copy()
            profile['created_at'] = datetime.now().isoformat()

        # Update profile with enrichment data
        profile['last_updated'] = datetime.now().isoformat()
        profile['last_lead_score'] = enrichment_data.get('lead_score', 0)
        profile['last_communication_category'] = enrichment_data.get('communication_category', 'general')

        # Update communication count
        profile['communication_count'] = profile.get('communication_count', 0) + 1

        # Store updated profile
        redis_client.setex(profile_key, timedelta(days=365).total_seconds(), json.dumps(profile))

        logger.info(f"Customer profile updated: {customer_email}")
        return True

    except Exception as e:
        logger.error(f"Error updating customer profile: {e}")
        return False

@celery_app.task(bind=True, base=CRMEnrichmentTask)
def generate_alert(self, enrichment_data: Dict[str, Any]):
    """Generate alert for high-priority communications."""
    try:
        alert_data = {
            'type': 'high_priority_communication',
            'lead_score': enrichment_data.get('lead_score'),
            'category': enrichment_data.get('communication_category'),
            'customer_info': enrichment_data.get('customer_info', {}),
            'created_at': datetime.now().isoformat(),
            'urgency': 'high'
        }

        # Store alert (would send notification in production)
        import redis

        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )

        alert_key = f"hvac_crm:alert:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        redis_client.setex(alert_key, timedelta(days=7).total_seconds(), json.dumps(alert_data))

        logger.info(f"High-priority alert generated: {alert_key}")
        return True

    except Exception as e:
        logger.error(f"Error generating alert: {e}")
        return False

@celery_app.task(bind=True, base=CRMEnrichmentTask)
def store_communication_data(self, communication_data: Dict[str, Any]):
    """Store communication data."""
    try:
        import redis

        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )

        key = f"hvac_crm:communication:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        redis_client.setex(key, timedelta(days=30).total_seconds(), json.dumps(communication_data))

        logger.info(f"Communication data stored: {key}")
        return True

    except Exception as e:
        logger.error(f"Error storing communication data: {e}")
        return False

@celery_app.task(bind=True, base=CRMEnrichmentTask)
def execute_workflow_task(self, task: Dict[str, Any], communication_data: Dict[str, Any]):
    """Execute workflow task."""
    try:
        task_type = task.get('type')

        if task_type == 'follow_up':
            # Schedule follow-up (would integrate with calendar system)
            logger.info(f"Follow-up task scheduled: {task.get('description')}")
        elif task_type == 'site_survey':
            # Schedule site survey
            logger.info(f"Site survey scheduled: {task.get('description')}")
        elif task_type == 'service_appointment':
            # Schedule service appointment
            logger.info(f"Service appointment scheduled: {task.get('description')}")

        # Store task execution
        import redis

        redis_client = redis.Redis(
            host=config.get('redis.host', '**************'),
            port=config.get('redis.port', 3037),
            db=config.get('redis.db', 0),
            decode_responses=True
        )

        task_key = f"hvac_crm:workflow_task:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_data = {
            'task': task,
            'communication_data': communication_data,
            'executed_at': datetime.now().isoformat(),
            'status': 'completed'
        }

        redis_client.setex(task_key, timedelta(days=30).total_seconds(), json.dumps(task_data))

        logger.info(f"Workflow task executed: {task_type}")
        return True

    except Exception as e:
        logger.error(f"Error executing workflow task: {e}")
        return False
