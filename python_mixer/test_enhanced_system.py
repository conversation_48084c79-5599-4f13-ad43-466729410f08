#!/usr/bin/env python3
"""
🧪 Test Enhanced Gobeklitepe System
===================================

Quick test script for the enhanced Gobeklitepe system with GoSpine integration.
"""

import asyncio
import logging
import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from enhanced_gobeklitepe_system import EnhancedGobeklitepeSystem

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_system():
    """Test the enhanced Gobeklitepe system."""
    print("🚀 Testing Enhanced Gobeklitepe System")
    print("=" * 50)
    
    # Initialize system
    system = EnhancedGobeklitepeSystem()
    
    print("🔧 Initializing system...")
    success = await system.initialize()
    
    if success:
        print("✅ System initialized successfully")
    else:
        print("❌ System initialization failed")
        return
    
    # Test semantic analysis
    print("\n🧠 Testing semantic analysis...")
    test_content = "Moja klimatyzacja LG nie chłodzi prawidłowo. Potrzebuję pilnej naprawy! System przestał działać wczoraj wieczorem."
    
    analysis = await system.analyze_communication_semantic(
        content=test_content,
        customer_id="CUST001",
        communication_type="email"
    )
    
    print(f"📊 Analysis Results:")
    print(f"   Sentiment: {analysis.sentiment}")
    print(f"   Urgency: {analysis.urgency_level}")
    print(f"   Intent: {analysis.intent}")
    print(f"   Equipment: {analysis.equipment_mentioned}")
    print(f"   Issues: {analysis.issues_identified}")
    print(f"   Recommendations: {analysis.recommended_actions}")
    print(f"   Confidence: {analysis.confidence_score:.2f}")
    print(f"   Processing time: {analysis.processing_time:.2f}s")
    
    # Test semantic search
    print("\n🔍 Testing semantic search...")
    search_results = await system.search_semantic_knowledge("klimatyzacja LG problem")
    print(f"📋 Search Results: {search_results['result_count']} found")
    
    # Get system metrics
    print("\n📊 System Metrics:")
    metrics = system.get_system_metrics()
    for key, value in metrics.items():
        print(f"   {key}: {value}")
    
    print("\n✅ Test completed successfully!")


def test_weaviate_connection():
    """Test Weaviate connection."""
    print("🧠 Testing Weaviate connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:8082/v1/.well-known/live", timeout=5)
        if response.status_code == 200:
            print("✅ Weaviate is live")
        else:
            print(f"⚠️ Weaviate returned status: {response.status_code}")
            
        response = requests.get("http://localhost:8082/v1/.well-known/ready", timeout=5)
        if response.status_code == 200:
            print("✅ Weaviate is ready")
        else:
            print(f"⚠️ Weaviate ready check returned: {response.status_code}")
            
        response = requests.get("http://localhost:8082/v1/meta", timeout=5)
        if response.status_code == 200:
            meta = response.json()
            print(f"✅ Weaviate meta: version {meta.get('version')}")
            print(f"   Modules: {list(meta.get('modules', {}).keys())}")
        else:
            print(f"⚠️ Weaviate meta returned: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Weaviate connection failed: {e}")


def test_gospine_connection():
    """Test GoSpine connection."""
    print("🔗 Testing GoSpine connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:8091/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ GoSpine API is healthy")
        else:
            print(f"⚠️ GoSpine health check returned: {response.status_code}")
            
        # Test some endpoints
        endpoints = [
            "/api/v1/pipeline/stages",
            "/api/v1/scoring/criteria"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"http://localhost:8091{endpoint}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {endpoint}: {len(data) if isinstance(data, list) else 'OK'}")
                else:
                    print(f"⚠️ {endpoint}: status {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
                
    except Exception as e:
        print(f"❌ GoSpine connection failed: {e}")


def main():
    """Main test function."""
    print("🔍 HVAC CRM System Integration Test")
    print("=" * 60)
    
    # Test connections first
    test_weaviate_connection()
    print()
    test_gospine_connection()
    print()
    
    # Test enhanced system
    try:
        asyncio.run(test_system())
    except Exception as e:
        print(f"❌ System test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
