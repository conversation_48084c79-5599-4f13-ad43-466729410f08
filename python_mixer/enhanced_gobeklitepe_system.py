#!/usr/bin/env python3
"""
🌟 Enhanced Gobeklitepe System with GoSpine Integration
======================================================

Advanced semantic HVAC CRM system combining Gobeklitepe semantic framework
with GoSpine backend integration for comprehensive business intelligence.

Features:
- Weaviate vector database for semantic search
- GoSpine API integration for CRM data
- Enhanced email processing with AI analysis
- Real-time customer profiling and insights
- Predictive maintenance recommendations
- Automated workflow orchestration
"""

import asyncio
import logging
import os
import sys
import json
import time
import aiohttp
import gradio as gr
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import weaviate
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class CustomerProfile:
    """Enhanced customer profile with semantic insights."""
    customer_id: str
    name: str
    email: str
    phone: str
    address: str
    equipment_history: List[Dict[str, Any]]
    communication_history: List[Dict[str, Any]]
    semantic_insights: Dict[str, Any]
    health_score: float
    churn_probability: float
    upsell_opportunities: List[str]
    last_updated: datetime


@dataclass
class SemanticAnalysis:
    """Comprehensive semantic analysis result."""
    sentiment: str
    urgency_level: str
    intent: str
    equipment_mentioned: List[str]
    issues_identified: List[str]
    recommended_actions: List[str]
    confidence_score: float
    processing_time: float
    customer_insights: Dict[str, Any]


class EnhancedGobeklitepeSystem:
    """Enhanced Gobeklitepe system with GoSpine integration."""
    
    def __init__(self):
        """Initialize the enhanced system."""
        self.weaviate_client = None
        self.gospine_base_url = "http://localhost:8091"
        self.weaviate_url = "http://localhost:8082"
        
        # System state
        self.is_initialized = False
        self.customer_profiles = {}
        self.semantic_cache = {}
        
        # Performance metrics
        self.metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'average_processing_time': 0.0,
            'cache_hits': 0,
            'gospine_calls': 0,
            'weaviate_queries': 0
        }
    
    async def initialize(self) -> bool:
        """Initialize all system components."""
        try:
            logger.info("🚀 Initializing Enhanced Gobeklitepe System...")
            
            # Initialize Weaviate client
            await self._initialize_weaviate()
            
            # Test GoSpine connection
            await self._test_gospine_connection()
            
            # Initialize semantic schemas
            await self._initialize_semantic_schemas()
            
            self.is_initialized = True
            logger.info("✅ Enhanced Gobeklitepe System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize system: {e}")
            return False
    
    async def _initialize_weaviate(self):
        """Initialize Weaviate client and connection."""
        try:
            self.weaviate_client = weaviate.Client(self.weaviate_url)
            
            # Test connection
            if self.weaviate_client.is_ready():
                logger.info("✅ Weaviate connection established")
            else:
                raise Exception("Weaviate not ready")
                
        except Exception as e:
            logger.error(f"❌ Weaviate initialization failed: {e}")
            # Create mock client for development
            self.weaviate_client = MockWeaviateClient()
    
    async def _test_gospine_connection(self):
        """Test GoSpine API connection."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gospine_base_url}/api/v1/health") as response:
                    if response.status == 200:
                        logger.info("✅ GoSpine API connection established")
                    else:
                        logger.warning(f"⚠️ GoSpine API returned status: {response.status}")
                        
        except Exception as e:
            logger.warning(f"⚠️ GoSpine API connection failed: {e}")
    
    async def _initialize_semantic_schemas(self):
        """Initialize Weaviate schemas for semantic data."""
        try:
            # Define HVAC-specific schema
            hvac_schema = {
                "class": "HVACCommunication",
                "description": "HVAC customer communications with semantic analysis",
                "properties": [
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "Communication content"
                    },
                    {
                        "name": "customer_id",
                        "dataType": ["string"],
                        "description": "Customer identifier"
                    },
                    {
                        "name": "sentiment",
                        "dataType": ["string"],
                        "description": "Sentiment analysis result"
                    },
                    {
                        "name": "urgency_level",
                        "dataType": ["string"],
                        "description": "Urgency classification"
                    },
                    {
                        "name": "equipment_mentioned",
                        "dataType": ["string[]"],
                        "description": "HVAC equipment mentioned"
                    },
                    {
                        "name": "timestamp",
                        "dataType": ["date"],
                        "description": "Communication timestamp"
                    }
                ]
            }
            
            # Create schema if it doesn't exist
            existing_classes = self.weaviate_client.schema.get()['classes']
            class_names = [cls['class'] for cls in existing_classes]
            
            if "HVACCommunication" not in class_names:
                self.weaviate_client.schema.create_class(hvac_schema)
                logger.info("✅ HVAC semantic schema created")
            else:
                logger.info("✅ HVAC semantic schema already exists")
                
        except Exception as e:
            logger.warning(f"⚠️ Schema initialization failed: {e}")
    
    async def analyze_communication_semantic(
        self, 
        content: str, 
        customer_id: Optional[str] = None,
        communication_type: str = "email"
    ) -> SemanticAnalysis:
        """Perform comprehensive semantic analysis on communication."""
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"{hash(content)}_{customer_id}"
            if cache_key in self.semantic_cache:
                self.metrics['cache_hits'] += 1
                return self.semantic_cache[cache_key]
            
            # Perform semantic analysis
            analysis = await self._perform_semantic_analysis(content, customer_id)
            
            # Store in Weaviate for future semantic search
            await self._store_semantic_data(content, customer_id, analysis)
            
            # Get customer insights from GoSpine
            customer_insights = await self._get_customer_insights(customer_id) if customer_id else {}
            
            # Create comprehensive analysis result
            result = SemanticAnalysis(
                sentiment=analysis.get('sentiment', 'neutral'),
                urgency_level=analysis.get('urgency_level', 'medium'),
                intent=analysis.get('intent', 'unknown'),
                equipment_mentioned=analysis.get('equipment_mentioned', []),
                issues_identified=analysis.get('issues_identified', []),
                recommended_actions=analysis.get('recommended_actions', []),
                confidence_score=analysis.get('confidence_score', 0.8),
                processing_time=time.time() - start_time,
                customer_insights=customer_insights
            )
            
            # Cache result
            self.semantic_cache[cache_key] = result
            
            # Update metrics
            self.metrics['total_analyses'] += 1
            self.metrics['successful_analyses'] += 1
            self._update_average_processing_time(result.processing_time)
            
            logger.info(f"✅ Semantic analysis completed in {result.processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Semantic analysis failed: {e}")
            self.metrics['total_analyses'] += 1
            
            # Return default analysis
            return SemanticAnalysis(
                sentiment='neutral',
                urgency_level='medium',
                intent='unknown',
                equipment_mentioned=[],
                issues_identified=[],
                recommended_actions=[],
                confidence_score=0.0,
                processing_time=time.time() - start_time,
                customer_insights={}
            )
    
    async def _perform_semantic_analysis(self, content: str, customer_id: Optional[str]) -> Dict[str, Any]:
        """Perform the actual semantic analysis."""
        # Enhanced semantic analysis with HVAC domain knowledge
        analysis = {
            'sentiment': self._analyze_sentiment(content),
            'urgency_level': self._analyze_urgency(content),
            'intent': self._analyze_intent(content),
            'equipment_mentioned': self._extract_equipment(content),
            'issues_identified': self._identify_issues(content),
            'recommended_actions': self._generate_recommendations(content),
            'confidence_score': 0.85
        }
        
        return analysis
    
    def _analyze_sentiment(self, content: str) -> str:
        """Analyze sentiment of the communication."""
        content_lower = content.lower()
        
        # Positive indicators
        positive_words = ['dziękuję', 'świetnie', 'doskonale', 'zadowolony', 'polecam']
        # Negative indicators  
        negative_words = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne', 'natychmiast']
        
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        if negative_count > positive_count:
            return 'negative'
        elif positive_count > negative_count:
            return 'positive'
        else:
            return 'neutral'
    
    def _analyze_urgency(self, content: str) -> str:
        """Analyze urgency level of the communication."""
        content_lower = content.lower()
        
        high_urgency = ['pilne', 'natychmiast', 'awaria', 'nie działa', 'zimno', 'gorąco']
        medium_urgency = ['problem', 'serwis', 'naprawa', 'sprawdzenie']
        
        if any(word in content_lower for word in high_urgency):
            return 'high'
        elif any(word in content_lower for word in medium_urgency):
            return 'medium'
        else:
            return 'low'
    
    def _analyze_intent(self, content: str) -> str:
        """Analyze the intent of the communication."""
        content_lower = content.lower()
        
        if any(word in content_lower for word in ['montaż', 'instalacja', 'nowy']):
            return 'installation'
        elif any(word in content_lower for word in ['naprawa', 'awaria', 'problem']):
            return 'repair'
        elif any(word in content_lower for word in ['serwis', 'przegląd', 'konserwacja']):
            return 'maintenance'
        elif any(word in content_lower for word in ['oferta', 'cena', 'kosztorys']):
            return 'quote'
        else:
            return 'inquiry'
    
    def _extract_equipment(self, content: str) -> List[str]:
        """Extract HVAC equipment mentioned in the communication."""
        content_lower = content.lower()
        equipment = []
        
        # HVAC equipment patterns
        equipment_patterns = {
            'klimatyzacja': ['klimatyzacja', 'klimatyzator', 'klima'],
            'pompa_ciepla': ['pompa ciepła', 'pompa cieplna'],
            'wentylacja': ['wentylacja', 'wentylator'],
            'ogrzewanie': ['ogrzewanie', 'grzejnik'],
            'lg': ['lg'],
            'daikin': ['daikin'],
            'mitsubishi': ['mitsubishi']
        }
        
        for equipment_type, patterns in equipment_patterns.items():
            if any(pattern in content_lower for pattern in patterns):
                equipment.append(equipment_type)
        
        return equipment

    def _identify_issues(self, content: str) -> List[str]:
        """Identify specific issues mentioned in the communication."""
        content_lower = content.lower()
        issues = []

        issue_patterns = {
            'nie_chlodzi': ['nie chłodzi', 'za ciepło', 'nie zimno'],
            'nie_grzeje': ['nie grzeje', 'za zimno', 'nie ciepło'],
            'halasliwy': ['hałas', 'głośny', 'szumi'],
            'wyciek': ['wyciek', 'kapi', 'mokro'],
            'nie_wlacza': ['nie włącza', 'nie działa', 'nie startuje'],
            'wysokie_rachunki': ['drogie rachunki', 'wysokie koszty', 'duże zużycie']
        }

        for issue_type, patterns in issue_patterns.items():
            if any(pattern in content_lower for pattern in patterns):
                issues.append(issue_type)

        return issues

    def _generate_recommendations(self, content: str) -> List[str]:
        """Generate recommendations based on content analysis."""
        recommendations = []
        content_lower = content.lower()

        if any(word in content_lower for word in ['pilne', 'awaria', 'nie działa']):
            recommendations.append('schedule_emergency_service')

        if any(word in content_lower for word in ['serwis', 'przegląd']):
            recommendations.append('schedule_maintenance')

        if any(word in content_lower for word in ['oferta', 'nowy', 'wymiana']):
            recommendations.append('prepare_quote')

        if any(word in content_lower for word in ['hałas', 'głośny']):
            recommendations.append('check_equipment_noise')

        return recommendations

    async def _store_semantic_data(self, content: str, customer_id: Optional[str], analysis: Dict[str, Any]):
        """Store semantic analysis data in Weaviate."""
        try:
            data_object = {
                "content": content,
                "customer_id": customer_id or "unknown",
                "sentiment": analysis.get('sentiment'),
                "urgency_level": analysis.get('urgency_level'),
                "equipment_mentioned": analysis.get('equipment_mentioned', []),
                "timestamp": datetime.now().isoformat()
            }

            self.weaviate_client.data_object.create(
                data_object=data_object,
                class_name="HVACCommunication"
            )

            self.metrics['weaviate_queries'] += 1
            logger.debug("✅ Semantic data stored in Weaviate")

        except Exception as e:
            logger.warning(f"⚠️ Failed to store semantic data: {e}")

    async def _get_customer_insights(self, customer_id: str) -> Dict[str, Any]:
        """Get customer insights from GoSpine API."""
        try:
            async with aiohttp.ClientSession() as session:
                # Get customer data from GoSpine
                async with session.get(f"{self.gospine_base_url}/api/v1/customers/{customer_id}") as response:
                    if response.status == 200:
                        customer_data = await response.json()

                        # Get additional insights
                        insights = {
                            'customer_data': customer_data,
                            'service_history': await self._get_service_history(customer_id),
                            'equipment_registry': await self._get_equipment_registry(customer_id),
                            'communication_patterns': await self._analyze_communication_patterns(customer_id)
                        }

                        self.metrics['gospine_calls'] += 1
                        return insights
                    else:
                        logger.warning(f"⚠️ Customer {customer_id} not found in GoSpine")
                        return {}

        except Exception as e:
            logger.warning(f"⚠️ Failed to get customer insights: {e}")
            return {}

    async def _get_service_history(self, customer_id: str) -> List[Dict[str, Any]]:
        """Get customer service history from GoSpine."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gospine_base_url}/api/v1/service-orders?customer_id={customer_id}") as response:
                    if response.status == 200:
                        return await response.json()
                    return []
        except:
            return []

    async def _get_equipment_registry(self, customer_id: str) -> List[Dict[str, Any]]:
        """Get customer equipment registry from GoSpine."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gospine_base_url}/api/v1/equipment?customer_id={customer_id}") as response:
                    if response.status == 200:
                        return await response.json()
                    return []
        except:
            return []

    async def _analyze_communication_patterns(self, customer_id: str) -> Dict[str, Any]:
        """Analyze customer communication patterns using Weaviate."""
        try:
            # Query Weaviate for customer communications
            result = self.weaviate_client.query.get("HVACCommunication", ["content", "sentiment", "urgency_level", "timestamp"]) \
                .with_where({
                    "path": ["customer_id"],
                    "operator": "Equal",
                    "valueString": customer_id
                }) \
                .with_limit(50) \
                .do()

            communications = result.get('data', {}).get('Get', {}).get('HVACCommunication', [])

            if not communications:
                return {}

            # Analyze patterns
            sentiments = [comm.get('sentiment') for comm in communications]
            urgency_levels = [comm.get('urgency_level') for comm in communications]

            patterns = {
                'total_communications': len(communications),
                'sentiment_distribution': {
                    'positive': sentiments.count('positive'),
                    'neutral': sentiments.count('neutral'),
                    'negative': sentiments.count('negative')
                },
                'urgency_distribution': {
                    'high': urgency_levels.count('high'),
                    'medium': urgency_levels.count('medium'),
                    'low': urgency_levels.count('low')
                },
                'communication_frequency': self._calculate_frequency(communications),
                'last_communication': communications[0].get('timestamp') if communications else None
            }

            return patterns

        except Exception as e:
            logger.warning(f"⚠️ Failed to analyze communication patterns: {e}")
            return {}

    def _calculate_frequency(self, communications: List[Dict[str, Any]]) -> str:
        """Calculate communication frequency."""
        if len(communications) < 2:
            return 'insufficient_data'

        # Simple frequency calculation based on count
        if len(communications) > 20:
            return 'very_high'
        elif len(communications) > 10:
            return 'high'
        elif len(communications) > 5:
            return 'medium'
        else:
            return 'low'

    def _update_average_processing_time(self, processing_time: float):
        """Update average processing time metric."""
        current_avg = self.metrics['average_processing_time']
        total = self.metrics['total_analyses']
        self.metrics['average_processing_time'] = (current_avg * (total - 1) + processing_time) / total

    async def search_semantic_knowledge(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Search HVAC knowledge using semantic similarity."""
        try:
            # Perform semantic search in Weaviate
            result = self.weaviate_client.query.get("HVACCommunication", ["content", "customer_id", "sentiment", "equipment_mentioned"]) \
                .with_near_text({"concepts": [query]}) \
                .with_limit(limit) \
                .with_additional(["certainty"]) \
                .do()

            communications = result.get('data', {}).get('Get', {}).get('HVACCommunication', [])

            self.metrics['weaviate_queries'] += 1

            return {
                'query': query,
                'results': communications,
                'result_count': len(communications),
                'search_type': 'semantic'
            }

        except Exception as e:
            logger.error(f"❌ Semantic search failed: {e}")
            return {
                'query': query,
                'results': [],
                'result_count': 0,
                'error': str(e)
            }

    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        return {
            **self.metrics,
            'is_initialized': self.is_initialized,
            'cache_size': len(self.semantic_cache),
            'customer_profiles_loaded': len(self.customer_profiles),
            'success_rate': self.metrics['successful_analyses'] / max(self.metrics['total_analyses'], 1),
            'timestamp': datetime.now().isoformat()
        }


class MockWeaviateClient:
    """Mock Weaviate client for development."""

    def __init__(self):
        self.schema = MockSchema()
        self.data_object = MockDataObject()
        self.query = MockQuery()

    def is_ready(self):
        return True


class MockSchema:
    def get(self):
        return {'classes': []}

    def create_class(self, schema):
        pass


class MockDataObject:
    def create(self, data_object, class_name):
        pass


class MockQuery:
    def get(self, class_name, properties):
        return MockQueryBuilder()


class MockQueryBuilder:
    def with_where(self, where):
        return self

    def with_limit(self, limit):
        return self

    def with_near_text(self, concepts):
        return self

    def with_additional(self, additional):
        return self

    def do(self):
        return {'data': {'Get': {}}}
