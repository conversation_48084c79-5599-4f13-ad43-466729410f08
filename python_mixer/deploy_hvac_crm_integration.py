#!/usr/bin/env python3
"""
🚀 DEPLOYMENT SCRIPT FOR HVAC CRM INTEGRATION
============================================

Comprehensive deployment script for the 4-phase GoSpine HVAC CRM integration.
Handles environment setup, service configuration, dependency installation, and system validation.

Features:
- UV-based Python environment setup
- Service health validation
- Environment variable configuration
- Celery worker deployment
- Comprehensive system testing
- Production readiness validation

Author: GoSpine HVAC CRM Integration
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from rich.text import Text

class HVACCRMDeployment:
    """Comprehensive deployment manager for HVAC CRM integration."""
    
    def __init__(self):
        self.console = Console()
        self.project_root = Path(__file__).parent
        self.deployment_results = {
            'start_time': datetime.now().isoformat(),
            'phases': {},
            'services': {},
            'environment': {},
            'validation': {},
            'status': 'running'
        }
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup deployment logging."""
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"deployment_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    async def deploy_hvac_crm_integration(self) -> Dict[str, Any]:
        """Deploy complete HVAC CRM integration."""
        self.console.print(Panel.fit(
            "[bold cyan]🚀 HVAC CRM INTEGRATION DEPLOYMENT[/bold cyan]\n"
            "[yellow]Deploying 4-phase GoSpine integration with production readiness[/yellow]",
            border_style="cyan"
        ))
        
        try:
            # Phase 1: Environment Setup
            await self._deploy_phase_1_environment()
            
            # Phase 2: Dependencies Installation
            await self._deploy_phase_2_dependencies()
            
            # Phase 3: Service Configuration
            await self._deploy_phase_3_services()
            
            # Phase 4: System Validation
            await self._deploy_phase_4_validation()
            
            # Final deployment report
            await self._generate_deployment_report()
            
            self.deployment_results['status'] = 'completed'
            
        except Exception as e:
            self.logger.error(f"Deployment failed: {e}")
            self.deployment_results['status'] = 'failed'
            self.deployment_results['error'] = str(e)
            
        return self.deployment_results
        
    async def _deploy_phase_1_environment(self):
        """Phase 1: Environment Setup."""
        phase_start = datetime.now()
        self.console.print("\n[bold blue]🔧 PHASE 1: Environment Setup[/bold blue]")
        
        environment_results = {}
        
        try:
            # Check UV installation
            uv_check = await self._check_uv_installation()
            environment_results['uv_installation'] = uv_check
            
            # Setup UV environment
            if uv_check['available']:
                uv_env_setup = await self._setup_uv_environment()
                environment_results['uv_environment'] = uv_env_setup
            
            # Validate environment variables
            env_validation = await self._validate_environment_variables()
            environment_results['environment_variables'] = env_validation
            
            # Create necessary directories
            dir_creation = await self._create_project_directories()
            environment_results['directory_structure'] = dir_creation
            
        except Exception as e:
            environment_results['error'] = str(e)
        
        self.deployment_results['phases']['phase_1'] = {
            'duration': (datetime.now() - phase_start).total_seconds(),
            'results': environment_results,
            'status': 'completed' if not environment_results.get('error') else 'failed'
        }
        
        self.console.print("[green]✅ Environment setup completed[/green]")
        
    async def _check_uv_installation(self) -> Dict[str, Any]:
        """Check UV installation."""
        try:
            result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                return {
                    'available': True,
                    'version': result.stdout.strip(),
                    'status': 'installed'
                }
            else:
                return {
                    'available': False,
                    'status': 'not_installed',
                    'message': 'UV not found. Please install UV: curl -LsSf https://astral.sh/uv/install.sh | sh'
                }
        except FileNotFoundError:
            return {
                'available': False,
                'status': 'not_found',
                'message': 'UV not found in PATH'
            }
            
    async def _setup_uv_environment(self) -> Dict[str, Any]:
        """Setup UV virtual environment."""
        try:
            # Create UV environment
            env_path = self.project_root / ".venv"
            
            if not env_path.exists():
                result = subprocess.run(
                    ['uv', 'venv', str(env_path)],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    return {
                        'created': True,
                        'path': str(env_path),
                        'status': 'success'
                    }
                else:
                    return {
                        'created': False,
                        'error': result.stderr,
                        'status': 'failed'
                    }
            else:
                return {
                    'created': False,
                    'path': str(env_path),
                    'status': 'already_exists'
                }
                
        except Exception as e:
            return {'created': False, 'error': str(e), 'status': 'failed'}
            
    async def _validate_environment_variables(self) -> Dict[str, Any]:
        """Validate required environment variables."""
        required_vars = [
            'DOLORES_EMAIL_PASSWORD',
            'GRZEGORZ_EMAIL_PASSWORD',
            'MINIO_ACCESS_KEY',
            'MINIO_SECRET_KEY'
        ]
        
        optional_vars = [
            'ELEVENLABS_API_KEY',
            'OPENAI_API_KEY',
            'ANTHROPIC_API_KEY'
        ]
        
        validation_results = {
            'required': {},
            'optional': {},
            'missing_required': [],
            'missing_optional': []
        }
        
        # Check required variables
        for var in required_vars:
            value = os.getenv(var)
            if value:
                validation_results['required'][var] = 'set'
            else:
                validation_results['required'][var] = 'missing'
                validation_results['missing_required'].append(var)
        
        # Check optional variables
        for var in optional_vars:
            value = os.getenv(var)
            if value:
                validation_results['optional'][var] = 'set'
            else:
                validation_results['optional'][var] = 'missing'
                validation_results['missing_optional'].append(var)
        
        validation_results['status'] = 'valid' if not validation_results['missing_required'] else 'invalid'
        
        return validation_results
        
    async def _create_project_directories(self) -> Dict[str, Any]:
        """Create necessary project directories."""
        directories = [
            'logs',
            'data/attachments',
            'data/transcriptions',
            'data/processed',
            'temp',
            'results'
        ]
        
        created_dirs = []
        
        for directory in directories:
            dir_path = self.project_root / directory
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(str(dir_path))
            except Exception as e:
                self.logger.error(f"Failed to create directory {dir_path}: {e}")
        
        return {
            'created_directories': created_dirs,
            'total_directories': len(directories),
            'status': 'completed'
        }
        
    async def _deploy_phase_2_dependencies(self):
        """Phase 2: Dependencies Installation."""
        phase_start = datetime.now()
        self.console.print("\n[bold blue]📦 PHASE 2: Dependencies Installation[/bold blue]")
        
        dependency_results = {}
        
        try:
            # Install Python dependencies with UV
            python_deps = await self._install_python_dependencies()
            dependency_results['python_dependencies'] = python_deps
            
            # Validate installations
            validation = await self._validate_dependency_installation()
            dependency_results['validation'] = validation
            
        except Exception as e:
            dependency_results['error'] = str(e)
        
        self.deployment_results['phases']['phase_2'] = {
            'duration': (datetime.now() - phase_start).total_seconds(),
            'results': dependency_results,
            'status': 'completed' if not dependency_results.get('error') else 'failed'
        }
        
        self.console.print("[green]✅ Dependencies installation completed[/green]")
        
    async def _install_python_dependencies(self) -> Dict[str, Any]:
        """Install Python dependencies using UV."""
        try:
            # Create requirements file if it doesn't exist
            requirements_file = self.project_root / "requirements_hvac_crm.txt"
            
            if not requirements_file.exists():
                await self._create_requirements_file(requirements_file)
            
            # Install dependencies
            result = subprocess.run(
                ['uv', 'pip', 'install', '-r', str(requirements_file)],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                return {
                    'installed': True,
                    'requirements_file': str(requirements_file),
                    'status': 'success'
                }
            else:
                return {
                    'installed': False,
                    'error': result.stderr,
                    'status': 'failed'
                }
                
        except Exception as e:
            return {'installed': False, 'error': str(e), 'status': 'failed'}
            
    async def _create_requirements_file(self, requirements_file: Path):
        """Create requirements file for HVAC CRM integration."""
        requirements = [
            "celery[redis]>=5.3.0",
            "redis>=5.0.0",
            "requests>=2.31.0",
            "rich>=13.0.0",
            "pydantic>=2.0.0",
            "python-dotenv>=1.0.0",
            "minio>=7.2.0",
            "pymongo>=4.6.0",
            "psycopg2-binary>=2.9.0",
            "sqlalchemy>=2.0.0",
            "asyncio-mqtt>=0.16.0",
            "aiofiles>=23.0.0",
            "httpx>=0.25.0",
            "uvloop>=0.19.0",
            "orjson>=3.9.0",
            "python-multipart>=0.0.6",
            "email-validator>=2.1.0",
            "cryptography>=41.0.0",
            "kombu>=5.3.0",
            "billiard>=4.2.0"
        ]
        
        with open(requirements_file, 'w') as f:
            f.write('\n'.join(requirements))
            
    async def _validate_dependency_installation(self) -> Dict[str, Any]:
        """Validate that dependencies are properly installed."""
        critical_packages = [
            'celery',
            'redis',
            'requests',
            'rich',
            'minio'
        ]
        
        validation_results = {}
        
        for package in critical_packages:
            try:
                __import__(package)
                validation_results[package] = 'available'
            except ImportError:
                validation_results[package] = 'missing'
        
        all_available = all(status == 'available' for status in validation_results.values())
        
        return {
            'packages': validation_results,
            'all_available': all_available,
            'status': 'valid' if all_available else 'invalid'
        }
        
    async def _deploy_phase_3_services(self):
        """Phase 3: Service Configuration."""
        phase_start = datetime.now()
        self.console.print("\n[bold blue]⚙️ PHASE 3: Service Configuration[/bold blue]")
        
        service_results = {}
        
        try:
            # Configure Celery workers
            celery_config = await self._configure_celery_workers()
            service_results['celery_workers'] = celery_config
            
            # Test service connections
            service_tests = await self._test_service_connections()
            service_results['service_connections'] = service_tests
            
        except Exception as e:
            service_results['error'] = str(e)
        
        self.deployment_results['phases']['phase_3'] = {
            'duration': (datetime.now() - phase_start).total_seconds(),
            'results': service_results,
            'status': 'completed' if not service_results.get('error') else 'failed'
        }
        
        self.console.print("[green]✅ Service configuration completed[/green]")
        
    async def _configure_celery_workers(self) -> Dict[str, Any]:
        """Configure Celery workers."""
        try:
            # Create Celery startup script
            startup_script = self.project_root / "start_celery_workers.sh"
            
            script_content = """#!/bin/bash
# HVAC CRM Celery Workers Startup Script

echo "Starting HVAC CRM Celery Workers..."

# Start Celery worker for email processing
celery -A celery_app worker --loglevel=info --queues=email_processing --concurrency=2 --hostname=email_worker@%h &

# Start Celery worker for M4A extraction
celery -A celery_app worker --loglevel=info --queues=m4a_extraction --concurrency=1 --hostname=m4a_worker@%h &

# Start Celery worker for transcription
celery -A celery_app worker --loglevel=info --queues=transcription --concurrency=1 --hostname=transcription_worker@%h &

# Start Celery worker for CRM enrichment
celery -A celery_app worker --loglevel=info --queues=crm_enrichment --concurrency=2 --hostname=crm_worker@%h &

# Start Celery worker for semantic analysis
celery -A celery_app worker --loglevel=info --queues=semantic_analysis --concurrency=1 --hostname=semantic_worker@%h &

# Start Celery beat scheduler
celery -A celery_app beat --loglevel=info &

echo "All Celery workers started successfully!"
"""
            
            with open(startup_script, 'w') as f:
                f.write(script_content)
            
            # Make script executable
            startup_script.chmod(0o755)
            
            return {
                'configured': True,
                'startup_script': str(startup_script),
                'status': 'success'
            }
            
        except Exception as e:
            return {'configured': False, 'error': str(e), 'status': 'failed'}

    async def _test_service_connections(self) -> Dict[str, Any]:
        """Test connections to external services."""
        services = {
            'Redis': ('**************', 3037),
            'MinIO': ('**************', 9000),
            'MongoDB': ('**************', 27017),
            'LM Studio': ('*************', 1234)
        }

        connection_results = {}

        for service_name, (host, port) in services.items():
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()

                connection_results[service_name] = {
                    'reachable': result == 0,
                    'host': host,
                    'port': port,
                    'status': 'connected' if result == 0 else 'unreachable'
                }

            except Exception as e:
                connection_results[service_name] = {
                    'reachable': False,
                    'error': str(e),
                    'status': 'error'
                }

        return {
            'services': connection_results,
            'all_reachable': all(r.get('reachable', False) for r in connection_results.values()),
            'status': 'success'
        }

    async def _deploy_phase_4_validation(self):
        """Phase 4: System Validation."""
        phase_start = datetime.now()
        self.console.print("\n[bold blue]✅ PHASE 4: System Validation[/bold blue]")

        validation_results = {}

        try:
            # Run comprehensive tests
            from comprehensive_pipeline_test import ComprehensivePipelineTest

            test_framework = ComprehensivePipelineTest()
            test_results = await test_framework.run_comprehensive_test()
            validation_results['comprehensive_tests'] = test_results

            # Validate deployment readiness
            readiness = await self._validate_deployment_readiness()
            validation_results['deployment_readiness'] = readiness

        except Exception as e:
            validation_results['error'] = str(e)

        self.deployment_results['phases']['phase_4'] = {
            'duration': (datetime.now() - phase_start).total_seconds(),
            'results': validation_results,
            'status': 'completed' if not validation_results.get('error') else 'failed'
        }

        self.console.print("[green]✅ System validation completed[/green]")

    async def _validate_deployment_readiness(self) -> Dict[str, Any]:
        """Validate deployment readiness."""
        readiness_checks = {
            'environment_variables': False,
            'dependencies': False,
            'service_connections': False,
            'file_structure': False,
            'celery_configuration': False
        }

        try:
            # Check environment variables
            required_vars = ['DOLORES_EMAIL_PASSWORD', 'GRZEGORZ_EMAIL_PASSWORD', 'MINIO_ACCESS_KEY', 'MINIO_SECRET_KEY']
            env_check = all(os.getenv(var) for var in required_vars)
            readiness_checks['environment_variables'] = env_check

            # Check dependencies
            try:
                import celery, redis, minio
                readiness_checks['dependencies'] = True
            except ImportError:
                readiness_checks['dependencies'] = False

            # Check file structure
            required_files = ['celery_app.py', 'config.yaml', 'tasks/__init__.py']
            file_check = all((self.project_root / f).exists() for f in required_files)
            readiness_checks['file_structure'] = file_check

            # Check Celery configuration
            celery_script = self.project_root / "start_celery_workers.sh"
            readiness_checks['celery_configuration'] = celery_script.exists()

        except Exception as e:
            self.logger.error(f"Readiness validation error: {e}")

        overall_readiness = all(readiness_checks.values())

        return {
            'checks': readiness_checks,
            'overall_ready': overall_readiness,
            'status': 'ready' if overall_readiness else 'not_ready'
        }

    async def _generate_deployment_report(self):
        """Generate comprehensive deployment report."""
        self.console.print("\n[bold blue]📊 GENERATING DEPLOYMENT REPORT[/bold blue]")

        # Calculate overall statistics
        total_duration = (datetime.now() - datetime.fromisoformat(self.deployment_results['start_time'])).total_seconds()

        # Create summary table
        table = Table(title="🚀 Deployment Results Summary")
        table.add_column("Phase", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Duration", style="yellow")
        table.add_column("Details", style="white")

        for phase_name, phase_data in self.deployment_results.get('phases', {}).items():
            status = "✅ Success" if phase_data.get('status') == 'completed' else "❌ Failed"
            duration = f"{phase_data.get('duration', 0):.2f}s"
            details = f"Phase {phase_name.split('_')[1]} deployment completed"

            table.add_row(phase_name.replace('_', ' ').title(), status, duration, details)

        self.console.print(table)

        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.project_root / f"deployment_report_{timestamp}.json"

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.deployment_results, f, indent=2, ensure_ascii=False)

        self.console.print(f"\n[green]📄 Detailed report saved: {report_file}[/green]")

        # Display deployment instructions
        self._display_deployment_instructions()

        # Display final summary
        self.console.print(Panel.fit(
            f"[bold green]🎉 HVAC CRM INTEGRATION DEPLOYMENT COMPLETED[/bold green]\n"
            f"[white]Total Duration: {total_duration:.2f}s[/white]\n"
            f"[white]Deployment Status: {self.deployment_results['status'].upper()}[/white]\n"
            f"[white]Report File: {report_file.name}[/white]",
            border_style="green"
        ))

    def _display_deployment_instructions(self):
        """Display post-deployment instructions."""
        instructions = """
[bold yellow]📋 POST-DEPLOYMENT INSTRUCTIONS[/bold yellow]

[bold cyan]1. Start Celery Workers:[/bold cyan]
   cd /home/<USER>/HVAC/unifikacja/GoSpine/python_mixer
   ./start_celery_workers.sh

[bold cyan]2. Monitor Workers:[/bold cyan]
   celery -A celery_app inspect active
   celery -A celery_app inspect stats

[bold cyan]3. Test Email Processing:[/bold cyan]
   python comprehensive_pipeline_test.py

[bold cyan]4. Monitor Logs:[/bold cyan]
   tail -f logs/celery.log
   tail -f logs/comprehensive_test_*.log

[bold cyan]5. Environment Variables Required:[/bold cyan]
   export DOLORES_EMAIL_PASSWORD="Blaeritipol1"
   export GRZEGORZ_EMAIL_PASSWORD="Blaeritipol1"
   export MINIO_ACCESS_KEY="your_minio_key"
   export MINIO_SECRET_KEY="your_minio_secret"

[bold cyan]6. Service Endpoints:[/bold cyan]
   - Redis: **************:3037
   - MinIO: **************:9000
   - MongoDB: **************:27017
   - LM Studio: *************:1234
   - NVIDIA NeMo STT: localhost:8889

[bold green]🚀 System is ready for production use![/bold green]
        """

        self.console.print(Panel(instructions, border_style="yellow"))

async def main():
    """Main deployment function."""
    deployment = HVACCRMDeployment()
    results = await deployment.deploy_hvac_crm_integration()
    return results

if __name__ == "__main__":
    asyncio.run(main())
