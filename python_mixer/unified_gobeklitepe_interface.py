#!/usr/bin/env python3
"""
🌟 Unified Gobeklitepe Interface with GoSpine Integration
========================================================

Advanced Gradio interface combining:
- Gobeklitepe semantic framework
- Weaviate vector database
- GoSpine HVAC CRM backend
- Real-time system monitoring
- Enhanced customer analytics

Features:
- System integrity monitoring
- Semantic email analysis
- Customer profile management
- Equipment registry integration
- Real-time dashboard
- Performance metrics
"""

import asyncio
import logging
import os
import sys
import json
import time
import requests
import gradio as gr
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Import our enhanced systems
from enhanced_gobeklitepe_system import EnhancedGobeklitepeSystem
from system_integrity_checker import SystemIntegrityChecker

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UnifiedGobeklitepeInterface:
    """Unified interface for Gobeklitepe system with GoSpine integration."""
    
    def __init__(self):
        """Initialize the unified interface."""
        self.gobeklitepe_system = EnhancedGobeklitepeSystem()
        self.integrity_checker = SystemIntegrityChecker()
        self.is_initialized = False
        
        # Service endpoints
        self.endpoints = {
            'hvac_crm': 'http://localhost:8080',
            'weaviate': 'http://localhost:8082',
            'gospine': 'http://localhost:8091'
        }
        
        # Cache for performance
        self.cache = {
            'customers': {},
            'equipment': {},
            'analytics': {},
            'last_update': None
        }
    
    async def initialize_systems(self):
        """Initialize all systems."""
        try:
            logger.info("🚀 Initializing Unified Gobeklitepe Interface...")
            
            # Initialize Gobeklitepe system
            success = await self.gobeklitepe_system.initialize()
            if success:
                logger.info("✅ Gobeklitepe system initialized")
            else:
                logger.warning("⚠️ Gobeklitepe system initialization failed")
            
            self.is_initialized = True
            return "✅ System initialized successfully!"
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return f"❌ Initialization failed: {e}"
    
    def check_system_health(self):
        """Check overall system health."""
        try:
            health_status = {}
            
            # Check HVAC CRM
            try:
                response = requests.get(f"{self.endpoints['hvac_crm']}/health", timeout=5)
                health_status['hvac_crm'] = {
                    'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                    'response_time': response.elapsed.total_seconds(),
                    'details': response.json() if response.status_code == 200 else None
                }
            except Exception as e:
                health_status['hvac_crm'] = {'status': 'error', 'error': str(e)}
            
            # Check Weaviate
            try:
                response = requests.get(f"{self.endpoints['weaviate']}/v1/.well-known/ready", timeout=5)
                health_status['weaviate'] = {
                    'status': 'ready' if response.status_code == 200 else 'not_ready',
                    'response_time': response.elapsed.total_seconds()
                }
            except Exception as e:
                health_status['weaviate'] = {'status': 'error', 'error': str(e)}
            
            # Get Gobeklitepe metrics
            if self.is_initialized:
                health_status['gobeklitepe'] = self.gobeklitepe_system.get_system_metrics()
            else:
                health_status['gobeklitepe'] = {'status': 'not_initialized'}
            
            # Format for display
            status_text = "🏥 **System Health Status**\n\n"
            
            for service, status in health_status.items():
                if status.get('status') in ['healthy', 'ready']:
                    status_text += f"✅ **{service.upper()}**: {status['status']}\n"
                    if 'response_time' in status:
                        status_text += f"   ⏱️ Response time: {status['response_time']:.3f}s\n"
                else:
                    status_text += f"❌ **{service.upper()}**: {status.get('status', 'unknown')}\n"
                    if 'error' in status:
                        status_text += f"   🔍 Error: {status['error']}\n"
                status_text += "\n"
            
            return status_text
            
        except Exception as e:
            return f"❌ Health check failed: {e}"
    
    async def analyze_email_content(self, email_content: str, customer_id: str = ""):
        """Analyze email content using Gobeklitepe semantic framework."""
        try:
            if not self.is_initialized:
                return "❌ System not initialized. Please initialize first."
            
            if not email_content.strip():
                return "❌ Please provide email content to analyze."
            
            # Perform semantic analysis
            analysis = await self.gobeklitepe_system.analyze_communication_semantic(
                content=email_content,
                customer_id=customer_id if customer_id.strip() else None,
                communication_type="email"
            )
            
            # Format results
            result_text = "🧠 **Semantic Analysis Results**\n\n"
            result_text += f"📊 **Sentiment**: {analysis.sentiment.upper()}\n"
            result_text += f"🚨 **Urgency Level**: {analysis.urgency_level.upper()}\n"
            result_text += f"🎯 **Intent**: {analysis.intent.upper()}\n"
            result_text += f"⚙️ **Equipment Mentioned**: {', '.join(analysis.equipment_mentioned) if analysis.equipment_mentioned else 'None'}\n"
            result_text += f"🔍 **Issues Identified**: {', '.join(analysis.issues_identified) if analysis.issues_identified else 'None'}\n"
            result_text += f"💡 **Recommended Actions**: {', '.join(analysis.recommended_actions) if analysis.recommended_actions else 'None'}\n"
            result_text += f"🎯 **Confidence Score**: {analysis.confidence_score:.2f}\n"
            result_text += f"⏱️ **Processing Time**: {analysis.processing_time:.3f}s\n"
            
            if analysis.customer_insights:
                result_text += f"\n👤 **Customer Insights Available**: Yes\n"
            
            return result_text
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            return f"❌ Analysis failed: {e}"
    
    def get_customer_data(self, customer_id: str = ""):
        """Get customer data from HVAC CRM."""
        try:
            if not customer_id.strip():
                # Get all customers
                response = requests.get(f"{self.endpoints['hvac_crm']}/api/customers", timeout=10)
            else:
                # Get specific customer
                response = requests.get(f"{self.endpoints['hvac_crm']}/api/customers/{customer_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, list):
                    # Multiple customers
                    df = pd.DataFrame(data)
                    return df.to_string(index=False) if not df.empty else "No customers found"
                else:
                    # Single customer
                    result_text = "👤 **Customer Information**\n\n"
                    for key, value in data.items():
                        result_text += f"**{key.replace('_', ' ').title()}**: {value}\n"
                    return result_text
            else:
                return f"❌ Failed to get customer data: {response.status_code}"
                
        except Exception as e:
            return f"❌ Error getting customer data: {e}"
    
    def get_equipment_registry(self, customer_id: str = ""):
        """Get equipment registry data."""
        try:
            url = f"{self.endpoints['hvac_crm']}/api/equipment"
            if customer_id.strip():
                url += f"?customer_id={customer_id}"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, list) and data:
                    df = pd.DataFrame(data)
                    return df.to_string(index=False)
                else:
                    return "No equipment found"
            else:
                return f"❌ Failed to get equipment data: {response.status_code}"
                
        except Exception as e:
            return f"❌ Error getting equipment data: {e}"
    
    def get_dashboard_overview(self):
        """Get dashboard overview from HVAC CRM."""
        try:
            response = requests.get(f"{self.endpoints['hvac_crm']}/api/dashboard/overview", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                result_text = "📊 **Dashboard Overview**\n\n"
                
                # Format the overview data
                for section, values in data.items():
                    result_text += f"**{section.replace('_', ' ').title()}**:\n"
                    if isinstance(values, dict):
                        for key, value in values.items():
                            result_text += f"  • {key.replace('_', ' ').title()}: {value}\n"
                    else:
                        result_text += f"  • {values}\n"
                    result_text += "\n"
                
                return result_text
            else:
                return f"❌ Failed to get dashboard data: {response.status_code}"
                
        except Exception as e:
            return f"❌ Error getting dashboard data: {e}"
    
    async def search_semantic_knowledge(self, query: str):
        """Search knowledge using semantic similarity."""
        try:
            if not self.is_initialized:
                return "❌ System not initialized. Please initialize first."
            
            if not query.strip():
                return "❌ Please provide a search query."
            
            # Perform semantic search
            results = await self.gobeklitepe_system.search_semantic_knowledge(query, limit=10)
            
            result_text = f"🔍 **Semantic Search Results for: '{query}'**\n\n"
            result_text += f"📊 **Found {results['result_count']} results**\n\n"
            
            if results['results']:
                for i, result in enumerate(results['results'], 1):
                    result_text += f"**Result {i}:**\n"
                    result_text += f"  • Content: {result.get('content', 'N/A')[:200]}...\n"
                    result_text += f"  • Customer: {result.get('customer_id', 'Unknown')}\n"
                    result_text += f"  • Sentiment: {result.get('sentiment', 'Unknown')}\n"
                    if 'equipment_mentioned' in result:
                        result_text += f"  • Equipment: {', '.join(result['equipment_mentioned'])}\n"
                    result_text += "\n"
            else:
                result_text += "No results found.\n"
            
            return result_text
            
        except Exception as e:
            return f"❌ Search failed: {e}"
    
    async def run_system_integrity_check(self):
        """Run comprehensive system integrity check."""
        try:
            results = await self.integrity_checker.run_comprehensive_check()
            
            result_text = "🔍 **System Integrity Check Results**\n\n"
            
            # Summary
            total_checks = len(results.get('checks', {}))
            fixes_applied = len(results.get('fixes_applied', []))
            recommendations = len(results.get('recommendations', []))
            
            result_text += f"📊 **Summary**:\n"
            result_text += f"  • Total Checks: {total_checks}\n"
            result_text += f"  • Fixes Applied: {fixes_applied}\n"
            result_text += f"  • Recommendations: {recommendations}\n\n"
            
            # Key findings
            if results.get('recommendations'):
                result_text += "💡 **Key Recommendations**:\n"
                for i, rec in enumerate(results['recommendations'][:5], 1):
                    result_text += f"  {i}. {rec}\n"
                result_text += "\n"
            
            # Fixes applied
            if results.get('fixes_applied'):
                result_text += "🔧 **Fixes Applied**:\n"
                for fix in results['fixes_applied']:
                    result_text += f"  • {fix}\n"
                result_text += "\n"
            
            result_text += f"⏱️ **Check completed at**: {results.get('timestamp', 'Unknown')}\n"
            
            return result_text
            
        except Exception as e:
            return f"❌ Integrity check failed: {e}"


def create_interface():
    """Create the Gradio interface."""
    interface = UnifiedGobeklitepeInterface()
    
    with gr.Blocks(
        title="🌟 Unified Gobeklitepe HVAC CRM System",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .tab-nav {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        """
    ) as demo:
        
        gr.Markdown("""
        # 🌟 Unified Gobeklitepe HVAC CRM System
        
        **Advanced semantic analysis and CRM integration for HVAC business excellence**
        
        Combining Gobeklitepe semantic framework with GoSpine backend for comprehensive business intelligence.
        """)
        
        with gr.Tabs():
            # System Status Tab
            with gr.Tab("🏥 System Status"):
                gr.Markdown("### System Health and Initialization")
                
                with gr.Row():
                    init_btn = gr.Button("🚀 Initialize Systems", variant="primary")
                    health_btn = gr.Button("🔍 Check Health", variant="secondary")
                    integrity_btn = gr.Button("🛠️ Run Integrity Check", variant="secondary")
                
                status_output = gr.Textbox(
                    label="System Status",
                    lines=15,
                    max_lines=20,
                    interactive=False
                )
                
                # Event handlers
                init_btn.click(
                    fn=interface.initialize_systems,
                    outputs=status_output
                )
                
                health_btn.click(
                    fn=interface.check_system_health,
                    outputs=status_output
                )
                
                integrity_btn.click(
                    fn=interface.run_system_integrity_check,
                    outputs=status_output
                )
            
            # Semantic Analysis Tab
            with gr.Tab("🧠 Semantic Analysis"):
                gr.Markdown("### Email and Communication Analysis")
                
                with gr.Row():
                    with gr.Column():
                        email_content = gr.Textbox(
                            label="Email Content",
                            placeholder="Paste email content here for semantic analysis...",
                            lines=8
                        )
                        customer_id_input = gr.Textbox(
                            label="Customer ID (optional)",
                            placeholder="Enter customer ID for enhanced analysis"
                        )
                        analyze_btn = gr.Button("🔍 Analyze", variant="primary")
                    
                    with gr.Column():
                        analysis_output = gr.Textbox(
                            label="Analysis Results",
                            lines=15,
                            interactive=False
                        )
                
                analyze_btn.click(
                    fn=interface.analyze_email_content,
                    inputs=[email_content, customer_id_input],
                    outputs=analysis_output
                )
            
            # Knowledge Search Tab
            with gr.Tab("🔍 Knowledge Search"):
                gr.Markdown("### Semantic Knowledge Search")
                
                with gr.Row():
                    search_query = gr.Textbox(
                        label="Search Query",
                        placeholder="Enter search terms (e.g., 'klimatyzacja LG problem')"
                    )
                    search_btn = gr.Button("🔍 Search", variant="primary")
                
                search_output = gr.Textbox(
                    label="Search Results",
                    lines=15,
                    interactive=False
                )
                
                search_btn.click(
                    fn=interface.search_semantic_knowledge,
                    inputs=search_query,
                    outputs=search_output
                )
            
            # CRM Data Tab
            with gr.Tab("👥 CRM Data"):
                gr.Markdown("### Customer and Equipment Management")
                
                with gr.Tabs():
                    with gr.Tab("👤 Customers"):
                        with gr.Row():
                            customer_search = gr.Textbox(
                                label="Customer ID (leave empty for all)",
                                placeholder="Enter customer ID or leave empty"
                            )
                            get_customers_btn = gr.Button("📋 Get Customers", variant="primary")
                        
                        customers_output = gr.Textbox(
                            label="Customer Data",
                            lines=15,
                            interactive=False
                        )
                        
                        get_customers_btn.click(
                            fn=interface.get_customer_data,
                            inputs=customer_search,
                            outputs=customers_output
                        )
                    
                    with gr.Tab("⚙️ Equipment"):
                        with gr.Row():
                            equipment_search = gr.Textbox(
                                label="Customer ID (leave empty for all)",
                                placeholder="Enter customer ID or leave empty"
                            )
                            get_equipment_btn = gr.Button("🔧 Get Equipment", variant="primary")
                        
                        equipment_output = gr.Textbox(
                            label="Equipment Data",
                            lines=15,
                            interactive=False
                        )
                        
                        get_equipment_btn.click(
                            fn=interface.get_equipment_registry,
                            inputs=equipment_search,
                            outputs=equipment_output
                        )
            
            # Dashboard Tab
            with gr.Tab("📊 Dashboard"):
                gr.Markdown("### Business Intelligence Dashboard")
                
                dashboard_btn = gr.Button("📊 Load Dashboard", variant="primary")
                
                dashboard_output = gr.Textbox(
                    label="Dashboard Overview",
                    lines=20,
                    interactive=False
                )
                
                dashboard_btn.click(
                    fn=interface.get_dashboard_overview,
                    outputs=dashboard_output
                )
        
        gr.Markdown("""
        ---
        **🌟 Unified Gobeklitepe System** - Advanced HVAC CRM with semantic intelligence
        
        Powered by: Weaviate • GoSpine • Gradio • Python
        """)
    
    return demo


if __name__ == "__main__":
    print("🌟 Starting Unified Gobeklitepe Interface...")
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        show_error=True,
        debug=True
    )
